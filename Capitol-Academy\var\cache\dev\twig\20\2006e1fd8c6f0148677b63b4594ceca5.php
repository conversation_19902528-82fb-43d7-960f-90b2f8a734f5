<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/index.html.twig */
class __TwigTemplate_e27ba000df923a945ed89a8bb85b6399 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Instructor Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Instructor Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 8
        $context["page_config"] = ["page_title" => "Instructors Management", "page_icon" => "fas fa-chalkboard-teacher", "search_placeholder" => "Search instructors by name, email, or specialization...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_new"), "text" => "Add New Instructor", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Instructors", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 20
(isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 20, $this->source); })())), "icon" => "fas fa-chalkboard-teacher", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 27
(isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 27, $this->source); })()), function ($__instructor__) use ($context, $macros) { $context["instructor"] = $__instructor__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 27, $this->source); })()), "isActive", [], "any", false, false, false, 27); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 34
(isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 34, $this->source); })()), function ($__instructor__) use ($context, $macros) { $context["instructor"] = $__instructor__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 34, $this->source); })()), "isActive", [], "any", false, false, false, 34); })), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 41
(isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 41, $this->source); })()), function ($__instructor__) use ($context, $macros) { $context["instructor"] = $__instructor__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 41, $this->source); })()), "createdAt", [], "any", false, false, false, 41) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 41, $this->source); })()), "createdAt", [], "any", false, false, false, 41) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-user-plus", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 48
        yield "
";
        // line 49
        yield from $this->load("admin/instructor/index.html.twig", 49, "716745520")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 49, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 115
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 116
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.instructor-row',
        ['.instructor-name', '.instructor-email', '.instructor-specialization']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Instructor management functions
function toggleInstructorStatus(instructorId, instructorName, isActive) {
    showStatusModal(instructorName, isActive, function() {
        executeInstructorStatusToggle(instructorId);
    });
}

function deleteInstructor(instructorId, instructorName) {
    showDeleteModal(instructorName, function() {
        executeInstructorDelete(instructorId);
    });
}

// Actual execution functions
function executeInstructorStatusToggle(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the instructor status');
    });
}

function executeInstructorDelete(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the instructor');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  158 => 116,  145 => 115,  134 => 49,  131 => 48,  129 => 41,  128 => 34,  127 => 27,  126 => 20,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Instructor Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Instructor Management{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Instructors Management',
    'page_icon': 'fas fa-chalkboard-teacher',
    'search_placeholder': 'Search instructors by name, email, or specialization...',
    'create_button': {
        'url': path('admin_instructor_new'),
        'text': 'Add New Instructor',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Instructors',
            'value': instructors|length,
            'icon': 'fas fa-chalkboard-teacher',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': instructors|filter(instructor => instructor.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': instructors|filter(instructor => not instructor.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': instructors|filter(instructor => instructor.createdAt and instructor.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Photo'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Specialization'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for instructor in instructors %}
            {% set row_cells = [
                {
                    'content': instructor.profileImage ?
                        '<img src=\"' ~ asset('uploads/instructors/' ~ instructor.profileImage) ~ '\" alt=\"' ~ instructor.name ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\" onerror=\"this.src=\\'' ~ asset('images/instructors/instructor-default-pp.png') ~ '\\'\">' :
                        '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ instructor.name|slice(0,1)|upper ~ '</div>'
                },
                {
                    'content': '<h6 class=\"instructor-name mb-0 font-weight-bold text-dark\">' ~ instructor.name ~ '</h6>'
                },
                {
                    'content': instructor.email ?
                        '<a href=\"mailto:' ~ instructor.email ~ '\" class=\"instructor-email text-decoration-none\" style=\"color: #011a2d; font-weight: 500;\">' ~ instructor.email ~ '</a>' :
                        '<span class=\"text-muted\">N/A</span>'
                },
                {
                    'content': instructor.specialization ?
                        '<span class=\"badge instructor-specialization\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 0.4rem 0.6rem; border-radius: 6px;\">' ~ instructor.specialization ~ '</span>' :
                        '<span class=\"text-muted instructor-specialization\">Not specified</span>'
                },
                {
                    'content': instructor.isActive ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Instructor\"><i class=\"fas fa-edit\"></i></a>
                        <a href=\"' ~ path('admin_instructor_show', {'emailPrefix': instructor.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Instructor\"><i class=\"fas fa-eye\"></i></a>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (instructor.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (instructor.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (instructor.isActive ? 'Deactivate' : 'Activate') ~ ' Instructor\" onclick=\"toggleInstructorStatus(' ~ instructor.id ~ ', \\'' ~ instructor.name ~ '\\', ' ~ instructor.isActive ~ ')\"><i class=\"fas fa-' ~ (instructor.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Instructor\" onclick=\"deleteInstructor(' ~ instructor.id ~ ', \\'' ~ instructor.name ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'instructor-row',
            'empty_message': 'No instructors found',
            'empty_icon': 'fas fa-chalkboard-teacher',
            'empty_description': 'Get started by adding your first instructor.',
            'search_config': {
                'fields': ['.instructor-name', '.instructor-email', '.instructor-specialization']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.instructor-row',
        ['.instructor-name', '.instructor-email', '.instructor-specialization']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Instructor management functions
function toggleInstructorStatus(instructorId, instructorName, isActive) {
    showStatusModal(instructorName, isActive, function() {
        executeInstructorStatusToggle(instructorId);
    });
}

function deleteInstructor(instructorId, instructorName) {
    showDeleteModal(instructorName, function() {
        executeInstructorDelete(instructorId);
    });
}

// Actual execution functions
function executeInstructorStatusToggle(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the instructor status');
    });
}

function executeInstructorDelete(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the instructor');
    });
}
</script>
{% endblock %}
", "admin/instructor/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\index.html.twig");
    }
}


/* admin/instructor/index.html.twig */
class __TwigTemplate_e27ba000df923a945ed89a8bb85b6399___716745520 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 49
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 49);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 50
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 51
        yield "        <!-- Standardized Table -->
        ";
        // line 52
        $context["table_headers"] = [["text" => "Photo"], ["text" => "Name"], ["text" => "Email"], ["text" => "Specialization"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 60
        yield "
        ";
        // line 61
        $context["table_rows"] = [];
        // line 62
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 62, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["instructor"]) {
            // line 63
            yield "            ";
            $context["row_cells"] = [["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 65
$context["instructor"], "profileImage", [], "any", false, false, false, 65)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((((("<img src=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source,             // line 66
$context["instructor"], "profileImage", [], "any", false, false, false, 66)))) . "\" alt=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 66)) . "\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\" onerror=\"this.src='") . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/instructors/instructor-default-pp.png")) . "'\">")) : ((("<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">" . Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 67
$context["instructor"], "name", [], "any", false, false, false, 67), 0, 1))) . "</div>")))], ["content" => (("<h6 class=\"instructor-name mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["instructor"], "name", [], "any", false, false, false, 70)) . "</h6>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["instructor"], "email", [], "any", false, false, false, 73)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<a href=\"mailto:" . CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["instructor"], "email", [], "any", false, false, false, 74)) . "\" class=\"instructor-email text-decoration-none\" style=\"color: #011a2d; font-weight: 500;\">") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "email", [], "any", false, false, false, 74)) . "</a>")) : ("<span class=\"text-muted\">N/A</span>"))], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["instructor"], "specialization", [], "any", false, false, false, 78)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"badge instructor-specialization\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 0.4rem 0.6rem; border-radius: 6px;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["instructor"], "specialization", [], "any", false, false, false, 79)) . "</span>")) : ("<span class=\"text-muted instructor-specialization\">Not specified</span>"))], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 83
$context["instructor"], "isActive", [], "any", false, false, false, 83)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>"))], ["content" => (((((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_edit", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source,             // line 89
$context["instructor"], "emailPrefix", [], "any", false, false, false, 89)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Instructor\"><i class=\"fas fa-edit\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_show", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source,             // line 90
$context["instructor"], "emailPrefix", [], "any", false, false, false, 90)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Instructor\"><i class=\"fas fa-eye\"></i></a>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 92
$context["instructor"], "isActive", [], "any", false, false, false, 92)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . " 0%, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "isActive", [], "any", false, false, false, 92)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#5a6268") : ("#1e7e34"))) . " 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "isActive", [], "any", false, false, false, 92)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Instructor\" onclick=\"toggleInstructorStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "id", [], "any", false, false, false, 92)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 92)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "isActive", [], "any", false, false, false, 92)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "isActive", [], "any", false, false, false, 92)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Instructor\" onclick=\"deleteInstructor(") . CoreExtension::getAttribute($this->env, $this->source,             // line 93
$context["instructor"], "id", [], "any", false, false, false, 93)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["instructor"], "name", [], "any", false, false, false, 93)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 97
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 97, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 97, $this->source); })())]]);
            // line 98
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['instructor'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 99
        yield "
        ";
        // line 100
        yield from $this->load("components/admin_table.html.twig", 100)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 101
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 101, $this->source); })()), "rows" =>         // line 102
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 102, $this->source); })()), "row_class" => "instructor-row", "empty_message" => "No instructors found", "empty_icon" => "fas fa-chalkboard-teacher", "empty_description" => "Get started by adding your first instructor.", "search_config" => ["fields" => [".instructor-name", ".instructor-email", ".instructor-specialization"]]]));
        // line 111
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  633 => 111,  631 => 102,  630 => 101,  629 => 100,  626 => 99,  620 => 98,  617 => 97,  614 => 93,  612 => 92,  609 => 90,  607 => 89,  605 => 83,  604 => 79,  603 => 78,  602 => 74,  601 => 73,  600 => 70,  599 => 67,  598 => 66,  597 => 65,  595 => 63,  590 => 62,  588 => 61,  585 => 60,  583 => 52,  580 => 51,  567 => 50,  544 => 49,  158 => 116,  145 => 115,  134 => 49,  131 => 48,  129 => 41,  128 => 34,  127 => 27,  126 => 20,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Instructor Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Instructor Management{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Instructors Management',
    'page_icon': 'fas fa-chalkboard-teacher',
    'search_placeholder': 'Search instructors by name, email, or specialization...',
    'create_button': {
        'url': path('admin_instructor_new'),
        'text': 'Add New Instructor',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Instructors',
            'value': instructors|length,
            'icon': 'fas fa-chalkboard-teacher',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': instructors|filter(instructor => instructor.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': instructors|filter(instructor => not instructor.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': instructors|filter(instructor => instructor.createdAt and instructor.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Photo'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Specialization'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for instructor in instructors %}
            {% set row_cells = [
                {
                    'content': instructor.profileImage ?
                        '<img src=\"' ~ asset('uploads/instructors/' ~ instructor.profileImage) ~ '\" alt=\"' ~ instructor.name ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\" onerror=\"this.src=\\'' ~ asset('images/instructors/instructor-default-pp.png') ~ '\\'\">' :
                        '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ instructor.name|slice(0,1)|upper ~ '</div>'
                },
                {
                    'content': '<h6 class=\"instructor-name mb-0 font-weight-bold text-dark\">' ~ instructor.name ~ '</h6>'
                },
                {
                    'content': instructor.email ?
                        '<a href=\"mailto:' ~ instructor.email ~ '\" class=\"instructor-email text-decoration-none\" style=\"color: #011a2d; font-weight: 500;\">' ~ instructor.email ~ '</a>' :
                        '<span class=\"text-muted\">N/A</span>'
                },
                {
                    'content': instructor.specialization ?
                        '<span class=\"badge instructor-specialization\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 0.4rem 0.6rem; border-radius: 6px;\">' ~ instructor.specialization ~ '</span>' :
                        '<span class=\"text-muted instructor-specialization\">Not specified</span>'
                },
                {
                    'content': instructor.isActive ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Instructor\"><i class=\"fas fa-edit\"></i></a>
                        <a href=\"' ~ path('admin_instructor_show', {'emailPrefix': instructor.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Instructor\"><i class=\"fas fa-eye\"></i></a>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (instructor.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (instructor.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (instructor.isActive ? 'Deactivate' : 'Activate') ~ ' Instructor\" onclick=\"toggleInstructorStatus(' ~ instructor.id ~ ', \\'' ~ instructor.name ~ '\\', ' ~ instructor.isActive ~ ')\"><i class=\"fas fa-' ~ (instructor.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Instructor\" onclick=\"deleteInstructor(' ~ instructor.id ~ ', \\'' ~ instructor.name ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'instructor-row',
            'empty_message': 'No instructors found',
            'empty_icon': 'fas fa-chalkboard-teacher',
            'empty_description': 'Get started by adding your first instructor.',
            'search_config': {
                'fields': ['.instructor-name', '.instructor-email', '.instructor-specialization']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.instructor-row',
        ['.instructor-name', '.instructor-email', '.instructor-specialization']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Instructor management functions
function toggleInstructorStatus(instructorId, instructorName, isActive) {
    showStatusModal(instructorName, isActive, function() {
        executeInstructorStatusToggle(instructorId);
    });
}

function deleteInstructor(instructorId, instructorName) {
    showDeleteModal(instructorName, function() {
        executeInstructorDelete(instructorId);
    });
}

// Actual execution functions
function executeInstructorStatusToggle(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the instructor status');
    });
}

function executeInstructorDelete(instructorId) {
    fetch(`/admin/instructor/\${instructorId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the instructor');
    });
}
</script>
{% endblock %}
", "admin/instructor/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\index.html.twig");
    }
}
