<?php

namespace App\Repository;

use App\Entity\PromotionalBanner;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PromotionalBanner>
 */
class PromotionalBannerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PromotionalBanner::class);
    }

    /**
     * Find the currently active promotional banner
     */
    public function findActiveBanner(): ?PromotionalBanner
    {
        $qb = $this->createQueryBuilder('pb')
            ->where('pb.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('pb.createdAt', 'DESC')
            ->setMaxResults(1);

        $banner = $qb->getQuery()->getOneOrNullResult();

        // Check if banner is expired
        if ($banner && !$banner->isCurrentlyValid()) {
            return null;
        }

        return $banner;
    }

    /**
     * Find all banners ordered by creation date
     */
    public function findAllOrdered(): array
    {
        return $this->createQueryBuilder('pb')
            ->orderBy('pb.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active banners only
     */
    public function findActiveOnly(): array
    {
        return $this->createQueryBuilder('pb')
            ->where('pb.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('pb.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find expired banners
     */
    public function findExpiredBanners(): array
    {
        return $this->createQueryBuilder('pb')
            ->where('pb.endDate < :now')
            ->setParameter('now', new \DateTimeImmutable())
            ->orderBy('pb.endDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Deactivate expired banners
     */
    public function deactivateExpiredBanners(): int
    {
        return $this->createQueryBuilder('pb')
            ->update()
            ->set('pb.isActive', ':inactive')
            ->set('pb.updatedAt', ':now')
            ->where('pb.endDate < :now')
            ->andWhere('pb.isActive = :active')
            ->setParameter('inactive', false)
            ->setParameter('active', true)
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->execute();
    }

    /**
     * Get banner statistics
     */
    public function getBannerStats(): array
    {
        $total = $this->count([]);
        $active = $this->count(['isActive' => true]);
        $expired = count($this->findExpiredBanners());

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'expired' => $expired
        ];
    }

    public function save(PromotionalBanner $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(PromotionalBanner $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
