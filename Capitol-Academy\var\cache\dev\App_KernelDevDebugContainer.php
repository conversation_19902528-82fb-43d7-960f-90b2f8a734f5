<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerR8yDayf\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerR8yDayf/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerR8yDayf.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerR8yDayf\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerR8yDayf\App_KernelDevDebugContainer([
    'container.build_hash' => 'R8yDayf',
    'container.build_id' => '62e6a68f',
    'container.build_time' => 1752494462,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerR8yDayf');
