<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/category/show.html.twig */
class __TwigTemplate_948436f58a8bbc70ea70256e249421d8 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/show.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Category Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Category Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_category_index");
        yield "\">Categories</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 10, $this->source); })()), "name", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-folder mr-3\" style=\"font-size: 2rem;\"></i>
                        Category Details: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 37, $this->source); })()), "name", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">


                        <!-- Print Category Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Category Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Categories Button -->
                        <a href=\"";
        // line 55
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_category_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Category Name and Status Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Category Name -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\"></i>
                                        Category Name
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 83
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 83, $this->source); })()), "name", [], "any", false, false, false, 83), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Category Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 96
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 96, $this->source); })()), "active", [], "any", false, false, false, 96)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 97
            yield "                                            <span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check-circle mr-1\"></i> Active
                                            </span>
                                        ";
        } else {
            // line 101
            yield "                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-pause-circle mr-1\"></i> Inactive
                                            </span>
                                        ";
        }
        // line 105
        yield "                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Display Options Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Display in Courses -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                        Display in Courses
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 120
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 120, $this->source); })()), "displayInCourses", [], "any", false, false, false, 120)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 121
            yield "                                            <span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check mr-1\"></i> Yes
                                            </span>
                                        ";
        } else {
            // line 125
            yield "                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-times mr-1\"></i> No
                                            </span>
                                        ";
        }
        // line 129
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Display in Videos -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-video text-primary mr-1\"></i>
                                        Display in Videos
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 141
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 141, $this->source); })()), "displayInVideos", [], "any", false, false, false, 141)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 142
            yield "                                            <span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check mr-1\"></i> Yes
                                            </span>
                                        ";
        } else {
            // line 146
            yield "                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-times mr-1\"></i> No
                                            </span>
                                        ";
        }
        // line 150
        yield "                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                Category Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                ";
        // line 162
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["category"] ?? null), "description", [], "any", true, true, false, 162) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 162, $this->source); })()), "description", [], "any", false, false, false, 162)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 162, $this->source); })()), "description", [], "any", false, false, false, 162), "html", null, true)) : ("No description provided"));
        yield "
                            </div>
                        </div>

                        <!-- Timestamps Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Created At -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created At
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 176
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 176, $this->source); })()), "createdAt", [], "any", false, false, false, 176)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 176, $this->source); })()), "createdAt", [], "any", false, false, false, 176), "F j, Y \\a\\t g:i A"), "html", null, true)) : ("Not available"));
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Updated At -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-edit text-primary mr-1\"></i>
                                        Last Updated
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 189
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 189, $this->source); })()), "updatedAt", [], "any", false, false, false, 189)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 189, $this->source); })()), "updatedAt", [], "any", false, false, false, 189), "F j, Y \\a\\t g:i A"), "html", null, true)) : ("Not available"));
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
    </div>
</div>

<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: normal;
}

.enhanced-display-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form Label Icons */
.form-label i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Professional Badge Styling */
.badge {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Print Styles */
@media print {
    .print-two-column {
        page-break-inside: avoid;
    }
    
    .btn, .alert {
        display: none !important;
    }
    
    .card-header {
        background: #011a2d !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>

<script>
function printCategoryDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/category/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  399 => 189,  383 => 176,  366 => 162,  352 => 150,  346 => 146,  340 => 142,  338 => 141,  324 => 129,  318 => 125,  312 => 121,  310 => 120,  293 => 105,  287 => 101,  281 => 97,  279 => 96,  263 => 83,  232 => 55,  211 => 37,  201 => 29,  191 => 25,  188 => 24,  184 => 23,  181 => 22,  171 => 18,  168 => 17,  164 => 16,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Category Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Category Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_category_index') }}\">Categories</a></li>
<li class=\"breadcrumb-item active\">{{ category.name }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-folder mr-3\" style=\"font-size: 2rem;\"></i>
                        Category Details: {{ category.name }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">


                        <!-- Print Category Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Category Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Categories Button -->
                        <a href=\"{{ path('admin_category_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Category Name and Status Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Category Name -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\"></i>
                                        Category Name
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {{ category.name }}
                                    </div>
                                </div>
                            </div>

                            <!-- Category Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {% if category.active %}
                                            <span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check-circle mr-1\"></i> Active
                                            </span>
                                        {% else %}
                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-pause-circle mr-1\"></i> Inactive
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Display Options Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Display in Courses -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                        Display in Courses
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {% if category.displayInCourses %}
                                            <span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check mr-1\"></i> Yes
                                            </span>
                                        {% else %}
                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-times mr-1\"></i> No
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Display in Videos -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-video text-primary mr-1\"></i>
                                        Display in Videos
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {% if category.displayInVideos %}
                                            <span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-check mr-1\"></i> Yes
                                            </span>
                                        {% else %}
                                            <span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\">
                                                <i class=\"fas fa-times mr-1\"></i> No
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                Category Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                {{ category.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Timestamps Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Created At -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created At
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {{ category.createdAt ? category.createdAt|date('F j, Y \\\\a\\\\t g:i A') : 'Not available' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Updated At -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-edit text-primary mr-1\"></i>
                                        Last Updated
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {{ category.updatedAt ? category.updatedAt|date('F j, Y \\\\a\\\\t g:i A') : 'Not available' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
    </div>
</div>

<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: normal;
}

.enhanced-display-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form Label Icons */
.form-label i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Professional Badge Styling */
.badge {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Print Styles */
@media print {
    .print-two-column {
        page-break-inside: avoid;
    }
    
    .btn, .alert {
        display: none !important;
    }
    
    .card-header {
        background: #011a2d !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>

<script>
function printCategoryDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/category/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\category\\show.html.twig");
    }
}
