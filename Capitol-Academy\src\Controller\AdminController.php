<?php

namespace App\Controller;

use App\Entity\Admin;
use App\Entity\Contact;
use App\Entity\Course;
use App\Entity\CourseModule;
use App\Entity\Enrollment;
use App\Entity\Partner;
use App\Entity\Payment;
use App\Entity\Plan;
use App\Entity\PromotionalBanner;
use App\Entity\User;
use App\Form\AdminFormType;
use App\Form\PartnerType;

use App\Repository\AdminRepository;
use App\Repository\CategoryRepository;
use App\Repository\ContactRepository;
use App\Repository\CourseRepository;
use App\Repository\EnrollmentRepository;
use App\Repository\PartnerRepository;
use App\Repository\PaymentRepository;
use App\Repository\PlanRepository;
use App\Repository\PromotionalBannerRepository;
use App\Repository\UserRepository;
use App\Service\AdminPermissionService;
use App\Service\EmailUniquenessValidator;
use App\Service\IpAddressService;
use App\Service\ValidationService;
use App\Service\ErrorHandlingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\Exception\ORMException;
use Psr\Log\LoggerInterface;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class AdminController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRepository $userRepository,
        private CourseRepository $courseRepository,
        private PlanRepository $planRepository,
        private CategoryRepository $categoryRepository,
        private ContactRepository $contactRepository,
        private AdminRepository $adminRepository,
        private PartnerRepository $partnerRepository,
        private PromotionalBannerRepository $promotionalBannerRepository,
        private AdminPermissionService $permissionService,
        private EmailUniquenessValidator $emailValidator,
        private IpAddressService $ipAddressService,
        private ValidationService $validationService,
        private ErrorHandlingService $errorHandlingService,
        private ValidatorInterface $validator,
        private LoggerInterface $logger
    ) {}

    #[Route('/dashboard', name: 'admin_dashboard')]
    public function dashboard(EnrollmentRepository $enrollmentRepository, PaymentRepository $paymentRepository): Response
    {
        // Get dashboard statistics
        $stats = $this->getDashboardStats();

        // Get recent activity
        $recentUsers = $this->userRepository->findBy([], ['createdAt' => 'DESC'], 5);
        $recentContacts = $this->contactRepository->findBy([], ['createdAt' => 'DESC'], 5);

        // Get recent enrollments and payments
        $recentEnrollments = $enrollmentRepository->createQueryBuilder('e')
            ->leftJoin('e.user', 'u')
            ->leftJoin('e.course', 'c')
            ->leftJoin('e.payment', 'p')
            ->addSelect('u', 'c', 'p')
            ->orderBy('e.enrolledAt', 'DESC')
            ->setMaxResults(5)
            ->getQuery()
            ->getResult();

        $recentPayments = $paymentRepository->createQueryBuilder('p')
            ->leftJoin('p.user', 'u')
            ->leftJoin('p.course', 'c')
            ->addSelect('u', 'c')
            ->where('p.status = :status')
            ->setParameter('status', 'succeeded')
            ->orderBy('p.createdAt', 'DESC')
            ->setMaxResults(5)
            ->getQuery()
            ->getResult();

        return $this->render('admin/dashboard.html.twig', [
            'stats' => $stats,
            'recent_users' => $recentUsers,
            'recent_contacts' => $recentContacts,
            'recent_enrollments' => $recentEnrollments,
            'recent_payments' => $recentPayments,
        ]);
    }

    #[Route('/users', name: 'admin_users')]
    public function users(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('users.view')) {
            $this->addFlash('error', 'You do not have permission to access user management. Please contact your administrator.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;
        $search = $request->query->get('search', '');

        $queryBuilder = $this->userRepository->createQueryBuilder('u');

        if ($search) {
            $queryBuilder->andWhere('u.email LIKE :search OR u.firstName LIKE :search OR u.lastName LIKE :search')
                        ->setParameter('search', '%' . $search . '%');
        }

        $queryBuilder->orderBy('u.createdAt', 'DESC');

        $totalUsers = count($queryBuilder->getQuery()->getResult());
        $users = $queryBuilder->setFirstResult(($page - 1) * $limit)
                             ->setMaxResults($limit)
                             ->getQuery()
                             ->getResult();

        $totalPages = ceil($totalUsers / $limit);

        return $this->render('admin/users/index.html.twig', [
            'users' => $users,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'search' => $search,
            'total_users' => $totalUsers,
            'canEdit' => $this->permissionService->hasPermission('users.edit'),
            'canDelete' => $this->permissionService->hasPermission('users.delete'),
        ]);
    }

    #[Route('/users/{id}', name: 'admin_user_show', requirements: ['id' => '\d+'])]
    public function userShow(User $user): Response
    {
        return $this->render('admin/users/details.html.twig', [
            'user' => $user,
        ]);
    }

    #[Route('/users/{emailPrefix}', name: 'admin_user_details', requirements: ['emailPrefix' => '[a-zA-Z0-9\-_\.]+'])]
    public function userDetails(string $emailPrefix): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('users.view')) {
            $this->addFlash('error', 'You do not have permission to access user management. Please contact your administrator.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $user = $this->userRepository->findByEmailPrefix($emailPrefix);
        if (!$user) {
            throw $this->createNotFoundException('User not found.');
        }

        return $this->render('admin/users/show.html.twig', [
            'user' => $user,
        ]);
    }



    #[Route('/users/{id}/block', name: 'admin_user_block', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function blockUser(User $user): Response
    {
        // Check permissions
        $admin = $this->getUser();
        if (!$admin instanceof Admin || !$admin->hasPermission('users.edit')) {
            throw $this->createAccessDeniedException('You do not have permission to block users.');
        }

        $user->setIsBlocked(true);
        $user->setUpdatedAt(new \DateTimeImmutable());
        $this->entityManager->flush();

        $this->addFlash('success', "User {$user->getFullName()} has been blocked successfully!");

        return new Response('User blocked successfully', 200);
    }

    #[Route('/users/{id}/unblock', name: 'admin_user_unblock', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function unblockUser(User $user): Response
    {
        // Check permissions
        $admin = $this->getUser();
        if (!$admin instanceof Admin || !$admin->hasPermission('users.edit')) {
            throw $this->createAccessDeniedException('You do not have permission to unblock users.');
        }

        $user->setIsBlocked(false);
        $user->setUpdatedAt(new \DateTimeImmutable());
        $this->entityManager->flush();

        $this->addFlash('success', "User {$user->getFullName()} has been unblocked successfully!");

        return new Response('User unblocked successfully', 200);
    }

    #[Route('/users/{id}/toggle-status', name: 'admin_user_toggle_status', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function toggleUserStatus(User $user, Request $request): Response
    {
        // Check permissions
        $admin = $this->getUser();
        if (!$admin instanceof Admin || !$admin->hasPermission('users.edit')) {
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'You do not have permission to modify users.'], 403);
            }
            throw $this->createAccessDeniedException('You do not have permission to modify users.');
        }

        $user->setIsBlocked(!$user->isBlocked());
        $user->setUpdatedAt(new \DateTimeImmutable());
        $this->entityManager->flush();

        $status = $user->isBlocked() ? 'blocked' : 'unblocked';
        $message = "User {$user->getFullName()} has been {$status} successfully!";

        if ($request->isXmlHttpRequest()) {
            return $this->json([
                'success' => true,
                'message' => $message,
                'newStatus' => $user->isBlocked() ? 'blocked' : 'active'
            ]);
        }

        $this->addFlash('success', $message);
        return $this->redirectToRoute('admin_users');
    }

    #[Route('/users/{id}/delete', name: 'admin_user_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function deleteUser(User $user, Request $request): Response
    {
        // Check permissions
        $admin = $this->getUser();
        if (!$admin instanceof Admin || !$admin->hasPermission('users.delete')) {
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'You do not have permission to delete users.'], 403);
            }
            throw $this->createAccessDeniedException('You do not have permission to delete users.');
        }

        $userName = $user->getFullName();
        $this->entityManager->remove($user);
        $this->entityManager->flush();

        $message = "User {$userName} has been deleted successfully!";

        if ($request->isXmlHttpRequest()) {
            return $this->json(['success' => true, 'message' => $message]);
        }

        $this->addFlash('success', $message);
        return $this->redirectToRoute('admin_users');
    }

    #[Route('/courses/export', name: 'admin_courses_export')]
    public function exportCourses(): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('courses.read') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to export courses.');
            return $this->redirectToRoute('admin_courses');
        }

        $courses = $this->courseRepository->findAll();

        // Create CSV content
        $csvData = [];
        $csvData[] = [
            'Code', 'Title', 'Description', 'Category', 'Level', 'Mode', 'Price',
            'Duration (minutes)', 'Enrolled Count', 'Active Enrollments', 'Completed Count',
            'Certified Count', 'Average Rating', 'Total Reviews', 'View Count',
            'Is Active', 'Has Modules', 'Created At', 'Updated At', 'Learning Outcomes',
            'Features', 'Banner Image', 'Thumbnail Image'
        ];

        foreach ($courses as $course) {
            $csvData[] = [
                $course->getCode(),
                $course->getTitle(),
                $course->getDescription(),
                $course->getCategory(),
                $course->getLevel(),
                $course->getMode(),
                $course->getPrice(),
                $course->getDuration(),
                $course->getEnrolledCount(),
                $course->getActiveEnrollments(),
                $course->getCompletedCount(),
                $course->getCertifiedCount(),
                $course->getAverageRating(),
                $course->getTotalReviews(),
                $course->getViewCount(),
                $course->isActive() ? 'Yes' : 'No',
                $course->hasModules() ? 'Yes' : 'No',
                $course->getCreatedAt() ? $course->getCreatedAt()->format('Y-m-d H:i:s') : '',
                $course->getUpdatedAt() ? $course->getUpdatedAt()->format('Y-m-d H:i:s') : '',
                $course->getLearningOutcomes() ? implode('; ', $course->getLearningOutcomes()) : '',
                $course->getFeatures() ? implode('; ', $course->getFeatures()) : '',
                $course->getBannerImage() ?: '',
                $course->getThumbnailImage() ?: ''
            ];
        }

        // Create CSV response
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="courses_export_' . date('Y-m-d_H-i-s') . '.csv"');

        $output = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $response->setContent(stream_get_contents($output));
        fclose($output);

        return $response;
    }

    #[Route('/courses', name: 'admin_courses')]
    public function courses(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('courses.view')) {
            $this->addFlash('error', 'You do not have permission to access course management. Please contact your administrator.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;
        $search = $request->query->get('search', '');

        $queryBuilder = $this->courseRepository->createQueryBuilder('c');

        if ($search) {
            $queryBuilder->andWhere('c.title LIKE :search OR c.code LIKE :search OR c.description LIKE :search OR c.category LIKE :search OR c.level LIKE :search OR c.mode LIKE :search OR c.learningOutcomes LIKE :search OR c.features LIKE :search')
                        ->setParameter('search', '%' . $search . '%');
        }

        $queryBuilder->orderBy('c.created_at', 'DESC');

        $totalCourses = count($queryBuilder->getQuery()->getResult());
        $courses = $queryBuilder->setFirstResult(($page - 1) * $limit)
                               ->setMaxResults($limit)
                               ->getQuery()
                               ->getResult();

        $totalPages = ceil($totalCourses / $limit);

        return $this->render('admin/courses/index.html.twig', [
            'courses' => $courses,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'search' => $search,
            'total_courses' => $totalCourses,
        ]);
    }

    #[Route('/courses/create', name: 'admin_course_create')]
    public function createCourse(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('courses.create') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to create courses.');
            return $this->redirectToRoute('admin_courses');
        }

        $course = new Course();

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('course_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid security token. Please try again.');
                return $this->render('admin/courses/create.html.twig', [
                    'course' => $course
                ]);
            }
            $course->setTitle($request->request->get('title'));

            // Check for course code uniqueness
            $courseCode = $request->request->get('code');
            $existingCourse = $this->courseRepository->findOneBy(['code' => $courseCode]);
            if ($existingCourse) {
                $this->addFlash('error', 'Course code "' . $courseCode . '" already exists. Please choose a different code.');
                return $this->render('admin/courses/create.html.twig', [
                    'course' => $course,
                    'categories' => $this->categoryRepository->findForCourses()
                ]);
            }

            $course->setCode($courseCode);
            $course->setDescription($request->request->get('description'));

            // Handle category with null validation
            $category = $request->request->get('category');
            if (empty($category)) {
                $this->addFlash('error', 'Please select a category for the course.');
                return $this->render('admin/courses/create.html.twig', [
                    'course' => $course,
                    'categories' => $this->categoryRepository->findForCourses()
                ]);
            }
            $course->setCategory($category);

            $course->setLevel($request->request->get('level'));
            $course->setDuration($request->request->getInt('duration'));
            $course->setPrice($request->request->get('price'));
            $course->setActive($request->request->getBoolean('is_active', true));
            $course->setHasModules($request->request->getBoolean('has_modules', false));
            $course->setCreatedAt(new \DateTimeImmutable());
            $course->setUpdatedAt(new \DateTimeImmutable());

            // Handle learning outcomes
            $learningOutcomes = $request->request->all('learning_outcomes');
            $learningOutcomes = array_filter($learningOutcomes, fn($outcome) => !empty(trim($outcome)));
            $course->setLearningOutcomes($learningOutcomes);

            // Handle features
            $features = $request->request->all('features');
            $features = array_filter($features, fn($feature) => !empty(trim($feature)));
            $course->setFeatures($features);

            // Handle image uploads
            $this->handleImageUploads($request, $course);

            // Course code is used for routing instead of slug

            try {
                $this->entityManager->persist($course);
                $this->entityManager->flush();

                // Handle modules if enabled
                if ($course->hasModules()) {
                    $this->handleCourseModules($request, $course);
                }

                $this->addFlash('success', 'Course created successfully!');
                return $this->redirectToRoute('admin_courses');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating course: ' . $e->getMessage());
                return $this->render('admin/courses/create.html.twig', [
                    'course' => $course,
                    'categories' => $this->categoryRepository->findForCourses()
                ]);
            }
        }

        return $this->render('admin/courses/create.html.twig', [
            'course' => $course,
            'categories' => $this->categoryRepository->findForCourses()
        ]);
    }

    #[Route('/courses/{code}/edit', name: 'admin_course_edit')]
    public function editCourse(string $code, Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('courses.edit')) {
            $this->addFlash('error', 'You do not have permission to edit courses. Please contact your administrator.');
            return $this->redirectToRoute('admin_courses');
        }

        // Find course by code
        $course = $this->entityManager->getRepository(Course::class)->findOneBy(['code' => $code]);
        if (!$course) {
            throw $this->createNotFoundException('Course not found.');
        }

        if ($request->isMethod('POST')) {
            $course->setTitle($request->request->get('title'));

            // Check for course code uniqueness (excluding current course)
            $courseCode = $request->request->get('code');
            $existingCourse = $this->courseRepository->findOneBy(['code' => $courseCode]);
            if ($existingCourse && $existingCourse->getId() !== $course->getId()) {
                $this->addFlash('error', 'Course code "' . $courseCode . '" already exists. Please choose a different code.');
                return $this->render('admin/courses/edit.html.twig', [
                    'course' => $course,
                    'categories' => $this->categoryRepository->findForCourses()
                ]);
            }

            $course->setCode($courseCode);
            $course->setDescription($request->request->get('description'));

            // Handle category with null validation
            $category = $request->request->get('category');
            if (empty($category)) {
                $this->addFlash('error', 'Please select a category for the course.');
                return $this->render('admin/courses/edit.html.twig', [
                    'course' => $course,
                    'categories' => $this->categoryRepository->findForCourses()
                ]);
            }
            $course->setCategory($category);

            $course->setLevel($request->request->get('level'));
            $course->setDuration($request->request->getInt('duration'));
            $course->setPrice($request->request->get('price'));
            $course->setActive($request->request->getBoolean('is_active', true));
            $course->setHasModules($request->request->getBoolean('has_modules', false));
            $course->setUpdatedAt(new \DateTimeImmutable());

            // Handle learning outcomes
            $learningOutcomes = $request->request->all('learning_outcomes');
            $learningOutcomes = array_filter($learningOutcomes, fn($outcome) => !empty(trim($outcome)));
            $course->setLearningOutcomes($learningOutcomes);

            // Handle features
            $features = $request->request->all('features');
            $features = array_filter($features, fn($feature) => !empty(trim($feature)));
            $course->setFeatures($features);

            // Handle image uploads
            $this->handleImageUploads($request, $course);

            // Course code is used for routing instead of slug

            $this->entityManager->flush();

            // Handle modules if enabled
            if ($course->hasModules()) {
                $this->updateCourseModules($request, $course);
            } else {
                // If modules are disabled, remove all existing modules
                foreach ($course->getModules() as $module) {
                    $this->entityManager->remove($module);
                }
                $this->entityManager->flush();
            }

            $this->addFlash('success', 'Course updated successfully!');
            return $this->redirectToRoute('admin_courses');
        }

        return $this->render('admin/courses/edit.html.twig', [
            'course' => $course,
            'categories' => $this->categoryRepository->findForCourses()
        ]);
    }

    #[Route('/courses/{id}/delete', name: 'admin_course_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function deleteCourse(Course $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('courses.delete')) {
            return $this->json(['success' => false, 'message' => 'You do not have permission to delete courses. Please contact your administrator.'], 403);
        }

        try {
            $courseName = $course->getTitle();
            $courseCode = $course->getCode();

            // Check if course has enrollments or certifications
            $enrollmentCount = $this->entityManager->getRepository(\App\Entity\Enrollment::class)->count(['course' => $course]);
            $certificationCount = $this->entityManager->getRepository(\App\Entity\Certification::class)->count(['course' => $course]);
            $paymentCount = $this->entityManager->getRepository(\App\Entity\Payment::class)->count(['course' => $course]);

            // If course has active enrollments, suggest deactivation instead
            if ($enrollmentCount > 0) {
                return $this->json([
                    'success' => false,
                    'message' => 'Course already enrolled. Deactivate course instead.',
                    'type' => 'enrollment_exists'
                ], 400);
            }

            // Log the deletion attempt for audit purposes
            /** @var Admin $currentAdmin */
            $currentAdmin = $this->getUser();
            $this->logger->info('Course deletion initiated', [
                'course_id' => $course->getId(),
                'course_code' => $courseCode,
                'course_title' => $courseName,
                'enrollments_count' => $enrollmentCount,
                'certifications_count' => $certificationCount,
                'payments_count' => $paymentCount,
                'admin_user' => $currentAdmin->getUsername()
            ]);

            // IMPORTANT: Do NOT delete enrollments, certifications, or payments
            // These must be preserved for historical/audit purposes

            // Update foreign key references to null where possible to prevent constraint violations
            // Note: This depends on your database schema - some foreign keys might need to be nullable

            // Delete course images if they exist
            $imagesDeleted = [];
            if ($course->getThumbnailImage()) {
                $thumbnailPath = $this->getParameter('kernel.project_dir') . '/public' . $course->getThumbnailImage();
                if (file_exists($thumbnailPath)) {
                    if (unlink($thumbnailPath)) {
                        $imagesDeleted[] = 'thumbnail';
                    }
                }
            }

            if ($course->getBannerImage()) {
                $bannerPath = $this->getParameter('kernel.project_dir') . '/public' . $course->getBannerImage();
                if (file_exists($bannerPath)) {
                    if (unlink($bannerPath)) {
                        $imagesDeleted[] = 'banner';
                    }
                }
            }

            // Delete the course
            // Note: CourseModule and CourseReview entities will be automatically deleted
            // due to orphanRemoval: true in the Course entity relationships
            $this->entityManager->remove($course);
            $this->entityManager->flush();

            // Log successful deletion
            $this->logger->info('Course deleted successfully', [
                'course_code' => $courseCode,
                'course_title' => $courseName,
                'images_deleted' => $imagesDeleted,
                'preserved_enrollments' => $enrollmentCount,
                'preserved_certifications' => $certificationCount,
                'preserved_payments' => $paymentCount
            ]);

            $message = "Course '{$courseName}' deleted successfully!";
            if ($enrollmentCount > 0 || $certificationCount > 0 || $paymentCount > 0) {
                $message .= " Note: {$enrollmentCount} enrollment(s), {$certificationCount} certification(s), and {$paymentCount} payment record(s) have been preserved for audit purposes.";
            }

            return $this->json(['success' => true, 'message' => $message]);
        } catch (\Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException $e) {
            $this->logger->error('Foreign key constraint violation during course deletion', [
                'course_id' => $course->getId(),
                'error' => $e->getMessage()
            ]);
            return $this->json(['success' => false, 'message' => 'Cannot delete course due to existing references. Please contact your administrator.'], 400);
        } catch (\Exception $e) {
            $this->logger->error('Error during course deletion', [
                'course_id' => $course->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->json(['success' => false, 'message' => 'An error occurred while deleting the course. Please try again or contact your administrator.'], 500);
        }
    }

    #[Route('/courses/{id}/toggle-status', name: 'admin_course_toggle_status', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function toggleCourseStatus(Course $course, Request $request): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('courses.edit')) {
            // Handle AJAX requests
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'You do not have permission to modify courses.'], 403);
            }
            throw $this->createAccessDeniedException('You do not have permission to modify courses.');
        }

        $course->setActive(!$course->isActive());
        $course->setUpdatedAt(new \DateTimeImmutable());
        $this->entityManager->flush();

        $status = $course->isActive() ? 'activated' : 'deactivated';

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            return $this->json(['success' => true, 'message' => "Course '{$course->getTitle()}' {$status} successfully!"]);
        }

        $this->addFlash('success', "Course {$status} successfully!");

        // Check if we should redirect back to preview page
        $redirectTo = $request->request->get('redirect_to');
        if ($redirectTo === 'preview') {
            return $this->redirectToRoute('admin_course_preview', ['code' => $course->getCode()]);
        }

        return $this->redirectToRoute('admin_courses');
    }

    #[Route('/contacts', name: 'admin_contacts')]
    public function contacts(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('contacts.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to view contacts.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;
        $search = $request->query->get('search', '');

        $queryBuilder = $this->contactRepository->createQueryBuilder('c');

        if ($search) {
            $queryBuilder->andWhere('c.email LIKE :search OR c.fullName LIKE :search OR c.subject LIKE :search')
                        ->setParameter('search', '%' . $search . '%');
        }

        $queryBuilder->orderBy('c.createdAt', 'DESC');

        $totalContacts = count($queryBuilder->getQuery()->getResult());
        $contacts = $queryBuilder->setFirstResult(($page - 1) * $limit)
                                ->setMaxResults($limit)
                                ->getQuery()
                                ->getResult();

        $totalPages = ceil($totalContacts / $limit);

        return $this->render('admin/contacts/index.html.twig', [
            'contacts' => $contacts,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'search' => $search,
            'total_contacts' => $totalContacts,
        ]);
    }

    #[Route('/contacts/{slug}', name: 'admin_contact_show')]
    public function contactShow(string $slug): Response
    {
        // Find contact by slug
        $contact = $this->contactRepository->findBySlug($slug);
        if (!$contact) {
            throw $this->createNotFoundException('Contact not found.');
        }

        return $this->render('admin/contacts/show.html.twig', [
            'contact' => $contact,
        ]);
    }

    #[Route('/contacts/{slug}/preview', name: 'admin_contact_preview')]
    public function contactPreview(string $slug): Response
    {
        // Find contact by slug
        $contact = $this->contactRepository->findBySlug($slug);
        if (!$contact) {
            throw $this->createNotFoundException('Contact not found.');
        }

        return $this->render('admin/contacts/preview.html.twig', [
            'contact' => $contact,
        ]);
    }

    #[Route('/contacts/{slug}/toggle-processed', name: 'admin_contact_toggle_processed', methods: ['POST'])]
    public function toggleContactProcessed(string $slug, Request $request): Response
    {
        // Find contact by slug
        $contact = $this->contactRepository->findBySlug($slug);
        if (!$contact) {
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'Contact not found.'], 404);
            }
            throw $this->createNotFoundException('Contact not found.');
        }

        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('contacts.process') && !$currentAdmin->isMasterAdmin())) {
            // Handle AJAX requests
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'You do not have permission to process contacts.'], 403);
            }
            $this->addFlash('error', 'You do not have permission to process contacts.');
            return $this->redirectToRoute('admin_contacts');
        }

        $contact->setProcessed(!$contact->isProcessed());
        $this->entityManager->flush();

        $status = $contact->isProcessed() ? 'processed' : 'unprocessed';

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            return $this->json(['success' => true, 'message' => "Contact marked as {$status} successfully!"]);
        }

        // No flash message for non-AJAX requests
        return $this->redirectToRoute('admin_contact_show', ['slug' => $contact->getUrlSlug()]);
    }

    #[Route('/contacts/{slug}/delete', name: 'admin_contact_delete', methods: ['POST'])]
    public function deleteContact(string $slug, Request $request): Response
    {
        // Find contact by slug
        $contact = $this->contactRepository->findBySlug($slug);
        if (!$contact) {
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'Contact not found.'], 404);
            }
            throw $this->createNotFoundException('Contact not found.');
        }

        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('contacts.delete') && !$currentAdmin->isMasterAdmin())) {
            if ($request->isXmlHttpRequest()) {
                return $this->json(['success' => false, 'message' => 'You do not have permission to delete contacts.'], 403);
            }
            $this->addFlash('error', 'You do not have permission to delete contacts.');
            return $this->redirectToRoute('admin_contacts');
        }

        $contactName = $contact->getFullName();
        $this->entityManager->remove($contact);
        $this->entityManager->flush();

        $message = "Contact from '{$contactName}' deleted successfully!";

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            return $this->json(['success' => true, 'message' => $message]);
        }

        // No flash message for non-AJAX requests
        return $this->redirectToRoute('admin_contacts');
    }

    #[Route('/test-email', name: 'admin_test_email', methods: ['GET', 'POST'])]
    public function testEmail(Request $request, MailerInterface $mailer): Response
    {
        // Check permissions - only master admin can test email
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || !$currentAdmin->isMasterAdmin()) {
            throw $this->createAccessDeniedException('Only master admin can test email functionality.');
        }

        if ($request->isMethod('POST')) {
            try {
                $email = (new Email())
                    ->from('<EMAIL>')
                    ->to('<EMAIL>')
                    ->subject('Capitol Academy - Email Test')
                    ->html($this->renderView('emails/test_email.html.twig', [
                        'test_time' => new \DateTime(),
                        'admin_name' => $currentAdmin->getFullName()
                    ]));

                $mailer->send($email);

                $this->addFlash('success', 'Test email sent <NAME_EMAIL>!');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to send test email: ' . $e->getMessage());
            }
        }

        return $this->render('admin/test_email.html.twig');
    }

    #[Route('/add-admin', name: 'admin_add_admin')]
    public function addAdmin(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        // Check if current admin has permission to create admins
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('admin.create') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to create administrators.');
            return $this->redirectToRoute('admin_dashboard');
        }

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            $submittedToken = $request->request->get('_token');
            if (!$this->isCsrfTokenValid('admin_creation', $submittedToken)) {
                $this->addFlash('error', 'Invalid security token. Please try again.');
                return $this->redirectToRoute('admin_add_admin');
            }

            // Validate form data
            $username = trim($request->request->get('username'));
            $email = trim($request->request->get('email'));
            $firstName = trim($request->request->get('firstName'));
            $lastName = trim($request->request->get('lastName'));
            $password = $request->request->get('password');
            $confirmPassword = $request->request->get('confirmPassword');
            $permissions = $request->request->all('permissions') ?? [];

            // Basic validation
            $errors = [];

            if (empty($username) || strlen($username) < 3) {
                $errors[] = 'Username must be at least 3 characters long.';
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Please provide a valid email address.';
            }

            if (empty($firstName) || empty($lastName)) {
                $errors[] = 'First name and last name are required.';
            }

            if (empty($password) || strlen($password) < 8) {
                $errors[] = 'Password must be at least 8 characters long.';
            }

            if ($password !== $confirmPassword) {
                $errors[] = 'Passwords do not match.';
            }

            // Check for existing admin with same username or email
            $existingAdmin = $this->entityManager->getRepository(Admin::class)->findOneBy([
                'username' => $username
            ]);
            if ($existingAdmin) {
                $errors[] = 'An administrator with this username already exists.';
            }

            $existingAdmin = $this->entityManager->getRepository(Admin::class)->findOneBy([
                'email' => $email
            ]);
            if ($existingAdmin) {
                $errors[] = 'An administrator with this email already exists.';
            }

            if (empty($errors)) {
                try {
                    // Create new admin
                    $newAdmin = new Admin();
                    $newAdmin->setUsername($username);
                    $newAdmin->setEmail($email);
                    $newAdmin->setFirstName($firstName);
                    $newAdmin->setLastName($lastName);
                    $newAdmin->setPassword($passwordHasher->hashPassword($newAdmin, $password));
                    $newAdmin->setRoles(['ROLE_ADMIN']);
                    $newAdmin->setPermissions($permissions);
                    $newAdmin->setIsActive(true);
                    $newAdmin->setIpAddress($this->ipAddressService->getClientIpAddress($request));

                    // Validate the entity before persisting
                    $violations = $this->validator->validate($newAdmin);
                    if (count($violations) > 0) {
                        foreach ($violations as $violation) {
                            $errors[] = $violation->getPropertyPath() . ': ' . $violation->getMessage();
                        }
                    } else {
                        $this->entityManager->persist($newAdmin);
                        $this->entityManager->flush();

                        $this->addFlash('success', 'Administrator created successfully!');
                        return $this->redirectToRoute('admin_admins');
                    }

                } catch (UniqueConstraintViolationException $e) {
                    // Handle unique constraint violations specifically
                    error_log('Unique constraint violation: ' . $e->getMessage());
                    if (str_contains($e->getMessage(), 'username')) {
                        $errors[] = 'Username already exists. Please choose a different username.';
                    } elseif (str_contains($e->getMessage(), 'email')) {
                        $errors[] = 'Email already exists. Please choose a different email.';
                    } else {
                        $errors[] = 'A duplicate entry was found. Please check your input.';
                    }
                } catch (ORMException $e) {
                    // Handle ORM-specific exceptions
                    error_log('ORM error: ' . $e->getMessage());
                    error_log('Stack trace: ' . $e->getTraceAsString());
                    $errors[] = 'Database error occurred: ' . $e->getMessage();
                } catch (\Exception $e) {
                    // Log the actual error for debugging
                    error_log('Admin creation error: ' . $e->getMessage());
                    error_log('Stack trace: ' . $e->getTraceAsString());

                    $errors[] = 'An error occurred while creating the administrator: ' . $e->getMessage();
                }
            }

            // If there are errors, pass them to the template
            return $this->render('admin/admins/add.html.twig', [
                'admin' => $this->getUser(),
                'errors' => $errors,
                'formData' => [
                    'username' => $username,
                    'email' => $email,
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    'permissions' => $permissions
                ]
            ]);
        }

        return $this->render('admin/admins/add.html.twig', [
            'admin' => $this->getUser()
        ]);
    }

    #[Route('/admin/{id}/toggle-status', name: 'admin_toggle_status', methods: ['POST'])]
    public function toggleAdminStatus(int $id, AdminRepository $adminRepository): Response
    {
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('admin.edit') && !$currentAdmin->isMasterAdmin())) {
            return $this->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $admin = $adminRepository->find($id);
        if (!$admin) {
            return $this->json(['success' => false, 'message' => 'Administrator not found'], 404);
        }

        if ($admin->isMasterAdmin()) {
            return $this->json(['success' => false, 'message' => 'Cannot modify master administrator'], 403);
        }

        $admin->setIsActive(!$admin->isActive());
        $this->entityManager->flush();

        $status = $admin->isActive() ? 'activated' : 'blocked';
        return $this->json(['success' => true, 'message' => "Administrator {$status} successfully", 'newStatus' => $admin->isActive()]);
    }

    #[Route('/admin/{id}/delete', name: 'admin_delete', methods: ['POST'])]
    public function deleteAdmin(int $id, AdminRepository $adminRepository): Response
    {
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('admin.delete') && !$currentAdmin->isMasterAdmin())) {
            return $this->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $admin = $adminRepository->find($id);
        if (!$admin) {
            return $this->json(['success' => false, 'message' => 'Administrator not found'], 404);
        }

        if ($admin->isMasterAdmin()) {
            return $this->json(['success' => false, 'message' => 'Cannot delete master administrator'], 403);
        }

        if ($admin->getId() === $currentAdmin->getId()) {
            return $this->json(['success' => false, 'message' => 'Cannot delete your own account'], 403);
        }

        $this->entityManager->remove($admin);
        $this->entityManager->flush();

        return $this->json(['success' => true, 'message' => 'Administrator deleted successfully']);
    }

    private function getDashboardStats(): array
    {
        $totalUsers = $this->userRepository->count([]);
        $totalCourses = $this->courseRepository->count([]);
        $totalContacts = $this->contactRepository->count([]);
        
        // Users registered this month
        $thisMonth = new \DateTimeImmutable('first day of this month');
        $newUsersThisMonth = $this->userRepository->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->andWhere('u.createdAt >= :thisMonth')
            ->setParameter('thisMonth', $thisMonth)
            ->getQuery()
            ->getSingleScalarResult();
        
        // Active courses
        $activeCourses = $this->courseRepository->count(['is_active' => true]);
        
        // Unprocessed contacts
        $unprocessedContacts = $this->contactRepository->count(['isProcessed' => false]);
        
        return [
            'total_users' => $totalUsers,
            'total_courses' => $totalCourses,
            'total_contacts' => $totalContacts,
            'new_users_this_month' => $newUsersThisMonth,
            'active_courses' => $activeCourses,
            'unprocessed_contacts' => $unprocessedContacts,
        ];
    }

    #[Route('/courses/{code}/preview', name: 'admin_course_preview')]
    public function previewCourse(string $code): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('courses.read')) {
            throw $this->createAccessDeniedException('You do not have permission to preview courses.');
        }

        // Find course by code
        $course = $this->entityManager->getRepository(Course::class)->findOneBy(['code' => $code]);
        if (!$course) {
            throw $this->createNotFoundException('Course not found.');
        }

        return $this->render('admin/courses/preview.html.twig', [
            'course' => $course,
        ]);
    }

    private function handleImageUploads(Request $request, Course $course): void
    {
        // Handle thumbnail image upload
        $thumbnailFile = $request->files->get('thumbnail_image');
        if ($thumbnailFile) {
            // Delete old thumbnail if it exists
            $oldThumbnail = $course->getThumbnailImage();
            if ($oldThumbnail) {
                $this->deleteOldImage($oldThumbnail, 'thumbnails');
            }

            $thumbnailPath = $this->uploadImage($thumbnailFile, 'thumbnails');
            if ($thumbnailPath) {
                $course->setThumbnailImage($thumbnailPath);
            }
        }

        // Handle banner image upload
        $bannerFile = $request->files->get('banner_image');
        if ($bannerFile) {
            // Delete old banner if it exists
            $oldBanner = $course->getBannerImage();
            if ($oldBanner) {
                $this->deleteOldImage($oldBanner, 'banners');
            }

            $bannerPath = $this->uploadImage($bannerFile, 'banners');
            if ($bannerPath) {
                $course->setBannerImage($bannerPath);
            }
        }
    }

    private function uploadImage($file, string $directory): ?string
    {
        // Enhanced security validation
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        $maxFileSize = 10 * 1024 * 1024; // 10MB
        $minFileSize = 1024; // 1KB minimum

        // Validate file object
        if (!$file || !$file->isValid()) {
            $this->addFlash('error', 'Invalid file upload.');
            return null;
        }

        // Validate MIME type
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            $this->addFlash('error', 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
            return null;
        }

        // Validate file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            $this->addFlash('error', 'Invalid file extension.');
            return null;
        }

        // Validate file size
        if ($file->getSize() > $maxFileSize) {
            $this->addFlash('error', 'File size too large. Maximum size is 10MB.');
            return null;
        }

        if ($file->getSize() < $minFileSize) {
            $this->addFlash('error', 'File size too small. Minimum size is 1KB.');
            return null;
        }

        // Generate professional filename
        $fileName = $this->generateProfessionalFilename($directory, $extension);

        // Create upload directory with proper permissions
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/courses/' . $directory;
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                $this->addFlash('error', 'Failed to create upload directory.');
                return null;
            }
        }

        try {
            // Move file with error handling
            $file->move($uploadDir, $fileName);

            // Verify file was uploaded successfully
            $uploadedFile = $uploadDir . '/' . $fileName;
            if (!file_exists($uploadedFile)) {
                $this->addFlash('error', 'File upload verification failed.');
                return null;
            }

            return '/uploads/courses/' . $directory . '/' . $fileName;
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to upload image: ' . $e->getMessage());
            return null;
        }
    }

    private function generateProfessionalFilename(string $directory, string $extension): string
    {
        // Determine the base name based on directory
        $baseName = match($directory) {
            'thumbnails' => 'coursethumbnail',
            'banners' => 'coursebanner',
            default => 'courseimage'
        };

        // Find the next available number
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/courses/' . $directory;
        $counter = 1;

        do {
            $fileName = $baseName . sprintf('%04d', $counter) . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            $counter++;
        } while (file_exists($filePath));

        return $fileName;
    }

    private function deleteOldImage(?string $imagePath, string $directory): void
    {
        if (!$imagePath) {
            return;
        }

        // Extract filename from path (remove /uploads/courses/directory/ prefix)
        $filename = basename($imagePath);
        $fullPath = $this->getParameter('kernel.project_dir') . '/public/uploads/courses/' . $directory . '/' . $filename;

        if (file_exists($fullPath)) {
            try {
                unlink($fullPath);
            } catch (\Exception $e) {
                // Log error but don't fail the upload process
                error_log('Failed to delete old image: ' . $e->getMessage());
            }
        }
    }

    #[Route('/profile', name: 'admin_profile')]
    public function profile(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        $admin = $this->getUser();

        if (!$admin instanceof Admin) {
            throw $this->createAccessDeniedException('Access denied.');
        }

        // Handle form submissions
        if ($request->isMethod('POST')) {
            $action = $request->request->get('action');

            if ($action === 'change_password') {
                $currentPassword = $request->request->get('current_password');
                $newPassword = $request->request->get('new_password');
                $confirmPassword = $request->request->get('confirm_password');

                // Verify current password
                if (!$passwordHasher->isPasswordValid($admin, $currentPassword)) {
                    $this->addFlash('error', 'Current password is incorrect.');
                } elseif ($newPassword !== $confirmPassword) {
                    $this->addFlash('error', 'New passwords do not match.');
                } elseif (strlen($newPassword) < 6) {
                    $this->addFlash('error', 'New password must be at least 6 characters long.');
                } else {
                    $admin->setPassword($passwordHasher->hashPassword($admin, $newPassword));
                    $admin->setUpdatedAt(new \DateTimeImmutable());
                    $this->entityManager->flush();
                    $this->addFlash('success', 'Password changed successfully!');
                    return $this->redirectToRoute('admin_profile');
                }
            } elseif ($action === 'upload_profile_image') {
                $profileImageFile = $request->files->get('profile_image_file');
                if ($profileImageFile) {
                    // Use the enhanced upload method
                    $imagePath = $this->uploadProfileImage($profileImageFile);
                    if ($imagePath) {
                        $admin->setProfileImage($imagePath);
                        $admin->setUpdatedAt(new \DateTimeImmutable());
                        $this->entityManager->flush();
                        $this->addFlash('success', 'Profile image updated successfully!');
                        return $this->redirectToRoute('admin_profile');
                    }
                }
            } elseif ($action === 'update_profile') {
                $firstName = $request->request->get('first_name');
                $lastName = $request->request->get('last_name');
                $email = $request->request->get('email');

                if ($firstName && $lastName && $email) {
                    $admin->setFirstName($firstName);
                    $admin->setLastName($lastName);
                    $admin->setEmail($email);
                    $admin->setUpdatedAt(new \DateTimeImmutable());
                    $this->entityManager->flush();
                    $this->addFlash('success', 'Profile updated successfully!');
                    return $this->redirectToRoute('admin_profile');
                } else {
                    $this->addFlash('error', 'All fields are required.');
                }
            }
        }

        return $this->render('admin/profile/index.html.twig', [
            'admin' => $admin,
        ]);
    }





    private function handleCourseModules(Request $request, Course $course): void
    {
        $modulesData = $request->request->all('modules');

        if (!empty($modulesData)) {
            $moduleIndex = 1;
            foreach ($modulesData as $moduleData) {
                if (!empty($moduleData['title'])) {
                    $module = new CourseModule();

                    // Use provided module code or generate one
                    $moduleCode = !empty($moduleData['code']) ? $moduleData['code'] :
                                 $course->getCode() . 'M' . str_pad($moduleIndex, 2, '0', STR_PAD_LEFT);
                    $module->setCode($moduleCode);

                    $module->setTitle($moduleData['title']);
                    $module->setDescription($moduleData['description'] ?? '');
                    $module->setDuration((int)($moduleData['duration'] ?? 0));
                    $module->setPrice($moduleData['price'] ?? '0.00');
                    $module->setSortOrder((int)($moduleData['sort_order'] ?? $moduleIndex));
                    $module->setActive((bool)($moduleData['is_active'] ?? true));
                    $module->setCourse($course);
                    $module->setCreatedAt(new \DateTimeImmutable());
                    $module->setUpdatedAt(new \DateTimeImmutable());

                    // Handle learning outcomes
                    $learningOutcomes = [];
                    if (isset($moduleData['learning_outcomes']) && is_array($moduleData['learning_outcomes'])) {
                        $learningOutcomes = array_filter($moduleData['learning_outcomes'], fn($outcome) => !empty(trim($outcome)));
                    }
                    $module->setLearningOutcomes($learningOutcomes);

                    // Handle features
                    $features = [];
                    if (isset($moduleData['features']) && is_array($moduleData['features'])) {
                        $features = array_filter($moduleData['features'], fn($feature) => !empty(trim($feature)));
                    }
                    $module->setFeatures($features);

                    $this->entityManager->persist($module);
                    $moduleIndex++;
                }
            }
            $this->entityManager->flush();
        }
    }

    /**
     * Update course totals (duration and price) based on modules
     */
    private function updateCourseTotalsFromModules(Course $course): void
    {
        $modules = $course->getModules();

        $totalDuration = 0;
        $totalPrice = 0.00;

        foreach ($modules as $module) {
            if ($module->isActive()) {
                $totalDuration += $module->getDuration() ?? 0;
                $totalPrice += (float)($module->getPrice() ?? 0);
            }
        }

        $course->setDuration($totalDuration);
        $course->setPrice(number_format($totalPrice, 2, '.', ''));

        $this->entityManager->persist($course);
        $this->entityManager->flush();
    }

    /**
     * Update course modules intelligently to prevent data loss
     */
    private function updateCourseModules(Request $request, Course $course): void
    {
        $modulesData = $request->request->all('modules');
        $existingModules = $course->getModules()->toArray();
        $processedModuleIds = [];

        if (!empty($modulesData)) {
            $moduleIndex = 1;
            foreach ($modulesData as $moduleData) {
                if (!empty($moduleData['title'])) {
                    $module = null;

                    // Try to find existing module by ID or code
                    if (isset($moduleData['id']) && $moduleData['id']) {
                        foreach ($existingModules as $existingModule) {
                            if ($existingModule->getId() == $moduleData['id']) {
                                $module = $existingModule;
                                break;
                            }
                        }
                    }

                    // If no existing module found, create new one
                    if (!$module) {
                        $module = new CourseModule();
                        $moduleCode = $course->getCode() . 'M' . str_pad($moduleIndex, 2, '0', STR_PAD_LEFT);
                        $module->setCode($moduleCode);
                        $module->setCourse($course);
                    }

                    // Update module properties
                    $module->setTitle($moduleData['title']);
                    $module->setDescription($moduleData['description'] ?? '');
                    $module->setDuration((int)($moduleData['duration'] ?? 0));
                    $module->setPrice($moduleData['price'] ?? '0.00');
                    $module->setSortOrder((int)($moduleData['sort_order'] ?? $moduleIndex));
                    $module->setActive((bool)($moduleData['is_active'] ?? true));
                    $module->setUpdatedAt(new \DateTimeImmutable());

                    // Handle learning outcomes
                    $learningOutcomes = [];
                    if (isset($moduleData['learning_outcomes']) && is_array($moduleData['learning_outcomes'])) {
                        $learningOutcomes = array_filter($moduleData['learning_outcomes'], fn($outcome) => !empty(trim($outcome)));
                    }
                    $module->setLearningOutcomes($learningOutcomes);

                    // Handle features
                    $features = [];
                    if (isset($moduleData['features']) && is_array($moduleData['features'])) {
                        $features = array_filter($moduleData['features'], fn($feature) => !empty(trim($feature)));
                    }
                    $module->setFeatures($features);

                    $this->entityManager->persist($module);

                    if ($module->getId()) {
                        $processedModuleIds[] = $module->getId();
                    }

                    $moduleIndex++;
                }
            }
        }

        // Remove modules that were not in the submitted data
        foreach ($existingModules as $existingModule) {
            if (!in_array($existingModule->getId(), $processedModuleIds)) {
                $this->entityManager->remove($existingModule);
            }
        }

        $this->entityManager->flush();
    }

    // ==================== PLAN MANAGEMENT ====================

    #[Route('/plans', name: 'admin_plans')]
    public function plans(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('plans.view')) {
            $this->addFlash('error', 'You do not have permission to access plan management. Please contact your administrator.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;
        $search = $request->query->get('search', '');

        $queryBuilder = $this->planRepository->createQueryBuilder('p');

        if ($search) {
            $queryBuilder->andWhere('p.title LIKE :search OR p.code LIKE :search OR p.description LIKE :search OR p.category LIKE :search OR p.level LIKE :search')
                        ->setParameter('search', '%' . $search . '%');
        }

        $queryBuilder->orderBy('p.created_at', 'DESC');

        $totalPlans = count($queryBuilder->getQuery()->getResult());
        $plans = $queryBuilder->setFirstResult(($page - 1) * $limit)
                               ->setMaxResults($limit)
                               ->getQuery()
                               ->getResult();

        $totalPages = ceil($totalPlans / $limit);

        return $this->render('admin/plans/index.html.twig', [
            'plans' => $plans,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'search' => $search,
            'total_plans' => $totalPlans,
        ]);
    }

    #[Route('/plans/create', name: 'admin_plan_create')]
    public function createPlan(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('plans.create') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to create plans.');
            return $this->redirectToRoute('admin_plans');
        }

        $plan = new Plan();

        if ($request->isMethod('POST')) {
            $plan->setTitle($request->request->get('title'));

            // Check for plan code uniqueness
            $planCode = $request->request->get('code');
            $existingPlan = $this->planRepository->findOneBy(['code' => $planCode]);
            if ($existingPlan) {
                $this->addFlash('error', 'Plan code "' . $planCode . '" already exists. Please choose a different code.');
                return $this->render('admin/plans/create.html.twig', [
                    'plan' => $plan
                ]);
            }

            $plan->setCode($planCode);
            $plan->setDescription($request->request->get('description'));
            $plan->setPrice($request->request->get('price'));

            // Handle duration field - convert to integer or null for lifetime access
            $durationValue = $request->request->get('duration');
            if (!empty($durationValue) && is_numeric($durationValue)) {
                $plan->setDuration((int) $durationValue);
            } else {
                $plan->setDuration(null); // null means lifetime access
            }

            $plan->setActive($request->request->getBoolean('is_active', true));
            $plan->setCreatedAt(new \DateTimeImmutable());
            $plan->setUpdatedAt(new \DateTimeImmutable());

            // Handle video selection
            $selectedVideoIds = $request->request->all('videos');
            if (!empty($selectedVideoIds)) {
                foreach ($selectedVideoIds as $videoId) {
                    $video = $this->entityManager->getRepository(\App\Entity\Video::class)->find($videoId);
                    if ($video) {
                        $plan->addVideo($video);
                    }
                }
            }



            try {
                $this->entityManager->persist($plan);
                $this->entityManager->flush();

                $this->addFlash('success', 'Plan created successfully!');
                return $this->redirectToRoute('admin_plans');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating plan: ' . $e->getMessage());
                return $this->render('admin/plans/create.html.twig', [
                    'plan' => $plan
                ]);
            }
        }

        // Get all active videos for selection
        $videos = $this->entityManager->getRepository(\App\Entity\Video::class)->findBy(
            ['isActive' => true],
            ['category' => 'ASC', 'title' => 'ASC']
        );

        return $this->render('admin/plans/create.html.twig', [
            'plan' => $plan,
            'videos' => $videos
        ]);
    }

    #[Route('/plans/{code}/edit', name: 'admin_plan_edit')]
    public function editPlan(string $code, Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('plans.edit') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to edit plans.');
            return $this->redirectToRoute('admin_plans');
        }

        // Find plan by code
        $plan = $this->entityManager->getRepository(Plan::class)->findOneBy(['code' => $code]);
        if (!$plan) {
            throw $this->createNotFoundException('Plan not found.');
        }

        if ($request->isMethod('POST')) {
            $plan->setTitle($request->request->get('title'));

            // Check for plan code uniqueness (excluding current plan)
            $planCode = $request->request->get('code');
            $existingPlan = $this->planRepository->findOneBy(['code' => $planCode]);
            if ($existingPlan && $existingPlan->getId() !== $plan->getId()) {
                $this->addFlash('error', 'Plan code "' . $planCode . '" already exists. Please choose a different code.');
                return $this->render('admin/plans/edit.html.twig', [
                    'plan' => $plan
                ]);
            }

            $plan->setCode($planCode);
            $plan->setDescription($request->request->get('description'));
            $plan->setPrice($request->request->get('price'));

            // Handle duration field - convert to integer or null for lifetime access
            $durationValue = $request->request->get('duration');
            if (!empty($durationValue) && is_numeric($durationValue)) {
                $plan->setDuration((int) $durationValue);
            } else {
                $plan->setDuration(null); // null means lifetime access
            }

            $plan->setActive($request->request->getBoolean('is_active', true));
            $plan->setUpdatedAt(new \DateTimeImmutable());

            // Handle video selection - clear existing and add new
            $plan->getVideos()->clear();
            $selectedVideoIds = $request->request->all('videos');
            if (!empty($selectedVideoIds)) {
                foreach ($selectedVideoIds as $videoId) {
                    $video = $this->entityManager->getRepository(\App\Entity\Video::class)->find($videoId);
                    if ($video) {
                        $plan->addVideo($video);
                    }
                }
            }



            $this->entityManager->flush();

            $this->addFlash('success', 'Plan updated successfully!');
            return $this->redirectToRoute('admin_plans');
        }

        // Get all active videos for selection
        $videos = $this->entityManager->getRepository(\App\Entity\Video::class)->findBy(
            ['isActive' => true],
            ['category' => 'ASC', 'title' => 'ASC']
        );

        return $this->render('admin/plans/edit.html.twig', [
            'plan' => $plan,
            'categories' => $this->categoryRepository->findForPlans(),
            'videos' => $videos
        ]);
    }

    #[Route('/plans/{code}/preview', name: 'admin_plan_preview')]
    public function previewPlan(string $code): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('plans.read')) {
            throw $this->createAccessDeniedException('You do not have permission to preview plans.');
        }

        // Find plan by code
        $plan = $this->entityManager->getRepository(Plan::class)->findOneBy(['code' => $code]);
        if (!$plan) {
            throw $this->createNotFoundException('Plan not found.');
        }

        return $this->render('admin/plans/preview.html.twig', [
            'plan' => $plan,
        ]);
    }

    #[Route('/plans/{code}/toggle-status', name: 'admin_plan_toggle_status', methods: ['POST'])]
    public function togglePlanStatus(string $code, Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('plans.edit') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to modify plan status.');
            return $this->redirectToRoute('admin_plans');
        }

        // Find plan by code
        $plan = $this->entityManager->getRepository(Plan::class)->findOneBy(['code' => $code]);
        if (!$plan) {
            throw $this->createNotFoundException('Plan not found.');
        }

        $plan->setActive(!$plan->isActive());
        $plan->setUpdatedAt(new \DateTimeImmutable());
        $this->entityManager->flush();

        $status = $plan->isActive() ? 'activated' : 'deactivated';

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            return $this->json(['success' => true, 'message' => "Plan '{$plan->getTitle()}' {$status} successfully!"]);
        }

        $this->addFlash('success', "Plan {$status} successfully!");

        // Check if we should redirect back to preview page
        $redirectTo = $request->request->get('redirect_to');
        if ($redirectTo === 'preview') {
            return $this->redirectToRoute('admin_plan_preview', ['code' => $plan->getCode()]);
        }

        return $this->redirectToRoute('admin_plans');
    }



    /**
     * Upload profile image with enhanced security
     */
    private function uploadProfileImage($file): ?string
    {
        // Enhanced security validation for profile images
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        $maxFileSize = 5 * 1024 * 1024; // 5MB
        $minFileSize = 1024; // 1KB minimum

        // Validate file object
        if (!$file || !$file->isValid()) {
            $this->addFlash('error', 'Invalid file upload.');
            return null;
        }

        // Validate MIME type
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            $this->addFlash('error', 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
            return null;
        }

        // Validate file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            $this->addFlash('error', 'Invalid file extension.');
            return null;
        }

        // Validate file size
        if ($file->getSize() > $maxFileSize) {
            $this->addFlash('error', 'File size too large. Maximum size is 5MB.');
            return null;
        }

        if ($file->getSize() < $minFileSize) {
            $this->addFlash('error', 'File size too small. Minimum size is 1KB.');
            return null;
        }

        // Generate secure filename
        $fileName = 'admin-profile-' . uniqid() . '.' . $extension;

        // Create upload directory with proper permissions
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/profiles';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                $this->addFlash('error', 'Failed to create upload directory.');
                return null;
            }
        }

        try {
            // Move file with error handling
            $file->move($uploadDir, $fileName);

            // Verify file was uploaded successfully
            $uploadedFile = $uploadDir . '/' . $fileName;
            if (!file_exists($uploadedFile)) {
                $this->addFlash('error', 'File upload verification failed.');
                return null;
            }

            return $fileName; // Return just the filename, not the full path
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to upload profile image: ' . $e->getMessage());
            return null;
        }
    }

    #[Route('/admins', name: 'admin_admins')]
    public function listAdmins(AdminRepository $adminRepository): Response
    {
        // Check if current admin has permission to view admins
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('admin.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to view administrators.');
            return $this->redirectToRoute('admin_dashboard');
        }

        // Get all admins from database
        $admins = $adminRepository->findBy([], ['createdAt' => 'DESC']);

        // Calculate statistics
        $blockedAdmins = count(array_filter($admins, fn($admin) => !$admin->isActive()));
        $recentAdditions = count(array_filter($admins, fn($admin) =>
            $admin->getCreatedAt() >= new \DateTimeImmutable('-30 days')
        ));

        return $this->render('admin/admins/index.html.twig', [
            'admin' => $this->getUser(),
            'admins' => $admins,
            'stats' => [
                'total' => count($admins),
                'active' => count(array_filter($admins, fn($admin) => $admin->isActive())),
                'blocked' => $blockedAdmins,
                'recent' => $recentAdditions
            ]
        ]);
    }

    #[Route('/admin/{id}/view', name: 'admin_admin_view', requirements: ['id' => '\d+'])]
    public function viewAdmin(Admin $admin): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('admin.view')) {
            $this->addFlash('error', 'You do not have permission to view administrator details. Please contact your administrator.');
            return $this->redirectToRoute('admin_admins');
        }

        return $this->render('admin/admins/view.html.twig', [
            'admin' => $admin,
            'currentAdmin' => $this->getUser()
        ]);
    }

    #[Route('/admin/{id}/edit', name: 'admin_admin_edit', requirements: ['id' => '\d+'])]
    public function editAdmin(Admin $admin, Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('admin.edit')) {
            $this->addFlash('error', 'You do not have permission to edit administrators. Please contact your administrator.');
            return $this->redirectToRoute('admin_admins');
        }

        // Prevent editing master admin
        if ($admin->isMasterAdmin()) {
            $this->addFlash('error', 'Master administrator account cannot be edited.');
            return $this->redirectToRoute('admin_admins');
        }

        $form = $this->createForm(AdminFormType::class, $admin);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();
            $this->addFlash('success', 'Administrator updated successfully!');
            return $this->redirectToRoute('admin_admins');
        }

        return $this->render('admin/admins/edit.html.twig', [
            'form' => $form->createView(),
            'admin' => $admin,
            'currentAdmin' => $this->getUser()
        ]);
    }















    // ===== VIDEO MANAGEMENT =====

    #[Route('/videos', name: 'admin_videos')]
    public function videos(): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('videos.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to manage videos.');
            return $this->redirectToRoute('admin_dashboard');
        }

        // Redirect to the dedicated video controller
        return $this->redirectToRoute('admin_video_index');
    }













    #[Route('/orders', name: 'admin_orders')]
    public function orders(): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('orders.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to manage orders.');
            return $this->redirectToRoute('admin_dashboard');
        }

        // Redirect to the dedicated order controller
        return $this->redirectToRoute('admin_order_index');
    }

    // ===== PROMOTIONAL BANNER MANAGEMENT =====

    #[Route('/promotional-banners', name: 'admin_promotional_banners')]
    public function promotionalBanners(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('banners.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to manage promotional banners.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;
        $search = $request->query->get('search', '');

        $queryBuilder = $this->promotionalBannerRepository->createQueryBuilder('pb');

        if ($search) {
            $queryBuilder->andWhere('pb.title LIKE :search OR pb.description LIKE :search')
                        ->setParameter('search', '%' . $search . '%');
        }

        $queryBuilder->orderBy('pb.createdAt', 'DESC');

        $totalBanners = count($queryBuilder->getQuery()->getResult());
        $banners = $queryBuilder->setFirstResult(($page - 1) * $limit)
                               ->setMaxResults($limit)
                               ->getQuery()
                               ->getResult();

        $totalPages = ceil($totalBanners / $limit);

        // Get banner statistics
        $stats = $this->promotionalBannerRepository->getBannerStats();

        return $this->render('admin/promotional_banners/index.html.twig', [
            'banners' => $banners,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'search' => $search,
            'total_banners' => $totalBanners,
            'stats' => $stats,
        ]);
    }

    #[Route('/promotional-banners/create', name: 'admin_promotional_banner_create')]
    public function createPromotionalBanner(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('banners.create') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to create promotional banners.');
            return $this->redirectToRoute('admin_promotional_banners');
        }

        $banner = new PromotionalBanner();

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('promotional_banner_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->render('admin/promotional_banners/create.html.twig', ['banner' => $banner]);
            }

            try {
                // Set banner properties from form data
                $title = $request->request->get('title');
                $description = $request->request->get('description');
                $backgroundColor = $request->request->get('backgroundColor');
                $endDate = $request->request->get('endDate');

                if (empty($title)) {
                    throw new \InvalidArgumentException('Title is required.');
                }

                $banner->setTitle($title);
                $banner->setDescription($description ?: null);
                $banner->setBackgroundColor($backgroundColor ?: '#001427');
                $banner->setIsActive(true);

                // Handle end date
                if ($endDate) {
                    $banner->setEndDate(new \DateTimeImmutable($endDate));
                }

                $this->entityManager->persist($banner);
                $this->entityManager->flush();

                $this->addFlash('success', 'Promotional banner created successfully!');
                return $this->redirectToRoute('admin_promotional_banners');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to create promotional banner: ' . $e->getMessage());
            }
        }

        return $this->render('admin/promotional_banners/create.html.twig', [
            'banner' => $banner,
        ]);
    }

    #[Route('/promotional-banners/{id}/edit', name: 'admin_promotional_banner_edit', requirements: ['id' => '\d+'])]
    public function editPromotionalBanner(PromotionalBanner $banner, Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('banners.edit') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to edit promotional banners.');
            return $this->redirectToRoute('admin_promotional_banners');
        }

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('promotional_banner_edit_' . $banner->getId(), $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->render('admin/promotional_banners/edit.html.twig', ['banner' => $banner]);
            }

            try {
                // Set banner properties from form data
                $title = $request->request->get('title');
                $description = $request->request->get('description');
                $backgroundColor = $request->request->get('backgroundColor');
                $endDate = $request->request->get('endDate');

                if (empty($title)) {
                    throw new \InvalidArgumentException('Title is required.');
                }

                $banner->setTitle($title);
                $banner->setDescription($description ?: null);
                $banner->setBackgroundColor($backgroundColor ?: '#001427');

                // Handle end date
                if ($endDate) {
                    $banner->setEndDate(new \DateTimeImmutable($endDate));
                } else {
                    $banner->setEndDate(null);
                }

                $this->entityManager->persist($banner);
                $this->entityManager->flush();

                $this->addFlash('success', 'Promotional banner updated successfully!');
                return $this->redirectToRoute('admin_promotional_banners');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to update promotional banner: ' . $e->getMessage());
            }
        }

        return $this->render('admin/promotional_banners/edit.html.twig', [
            'banner' => $banner,
        ]);
    }

    #[Route('/promotional-banners/{id}/delete', name: 'admin_promotional_banner_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function deletePromotionalBanner(PromotionalBanner $banner, Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('banners.delete') && !$currentAdmin->isMasterAdmin())) {
            return $this->json(['success' => false, 'message' => 'You do not have permission to delete promotional banners.'], 403);
        }

        // Validate CSRF token
        if (!$this->isCsrfTokenValid('promotional_banner_delete', $request->request->get('_token'))) {
            return $this->json(['success' => false, 'message' => 'Invalid CSRF token.'], 400);
        }

        try {
            $bannerTitle = $banner->getTitle();
            $this->entityManager->remove($banner);
            $this->entityManager->flush();

            return $this->json(['success' => true, 'message' => "Promotional banner '{$bannerTitle}' deleted successfully!"]);
        } catch (\Exception $e) {
            return $this->json(['success' => false, 'message' => 'Failed to delete promotional banner: ' . $e->getMessage()], 500);
        }
    }

    #[Route('/promotional-banners/{id}/toggle-status', name: 'admin_promotional_banner_toggle_status', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function togglePromotionalBannerStatus(PromotionalBanner $banner): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('banners.edit') && !$currentAdmin->isMasterAdmin())) {
            return $this->json(['success' => false, 'message' => 'You do not have permission to modify promotional banners.'], 403);
        }

        $banner->setIsActive(!$banner->isActive());
        $this->entityManager->flush();

        $status = $banner->isActive() ? 'activated' : 'deactivated';

        return $this->json(['success' => true, 'message' => "Promotional banner {$status} successfully!"]);
    }

    // ==================== PARTNER MANAGEMENT METHODS ====================

    #[Route('/partners', name: 'admin_partners')]
    public function partnersManagement(Request $request): Response
    {
        $search = $request->query->get('search', '');
        $partners = $this->partnerRepository->findWithSearch($search);

        return $this->render('admin/partners/index.html.twig', [
            'partners' => $partners,
            'search' => $search,
            'stats' => $this->partnerRepository->getPartnerStats()
        ]);
    }

    #[Route('/partners/create', name: 'admin_partners_create')]
    public function createPartner(Request $request): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('partners.create') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to create partners.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $partner = new Partner();
        $partner->setDisplayOrder($this->partnerRepository->getNextDisplayOrder());

        $form = $this->createForm(PartnerType::class, $partner);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Handle logo file upload
                $logoFile = $form->get('logoFile')->getData();
                if ($logoFile) {
                    $originalFilename = pathinfo($logoFile->getClientOriginalName(), PATHINFO_FILENAME);
                    $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
                    $newFilename = $safeFilename . '-' . uniqid() . '.' . $logoFile->guessExtension();

                    try {
                        // Ensure uploads directory exists
                        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/partners';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }

                        $logoFile->move($uploadDir, $newFilename);
                        $partner->setLogoPath($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Error uploading logo file: ' . $e->getMessage());
                        return $this->render('admin/partners/create.html.twig', [
                            'form' => $form->createView(),
                            'partner' => $partner
                        ]);
                    }
                }

                $this->entityManager->persist($partner);
                $this->entityManager->flush();

                $this->addFlash('success', 'Partner created successfully!');
                return $this->redirectToRoute('admin_partners');

            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while creating the partner: ' . $e->getMessage());
                return $this->render('admin/partners/create.html.twig', [
                    'form' => $form->createView(),
                    'partner' => $partner
                ]);
            }
        }

        return $this->render('admin/partners/create.html.twig', [
            'form' => $form->createView(),
            'partner' => $partner
        ]);
    }

    #[Route('/partners/{id}', name: 'admin_partners_show', requirements: ['id' => '\d+'])]
    public function showPartner(Partner $partner): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('partners.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to view partners.');
            return $this->redirectToRoute('admin_dashboard');
        }

        return $this->render('admin/partners/show.html.twig', [
            'partner' => $partner
        ]);
    }

    #[Route('/partners/{slug}', name: 'admin_partners_show_by_slug', requirements: ['slug' => '[a-z0-9\-]+'])]
    public function showPartnerBySlug(string $slug): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('partners.view') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to view partners.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $partner = $this->partnerRepository->findBySlug($slug);
        if (!$partner) {
            throw $this->createNotFoundException('Partner not found.');
        }

        return $this->render('admin/partners/show.html.twig', [
            'partner' => $partner
        ]);
    }

    #[Route('/partners/{id}/edit', name: 'admin_partner_edit', requirements: ['id' => '\d+'])]
    public function editPartner(Request $request, Partner $partner): Response
    {
        // Check permissions
        /** @var Admin $currentAdmin */
        $currentAdmin = $this->getUser();
        if (!$currentAdmin instanceof Admin || (!$currentAdmin->hasPermission('partners.edit') && !$currentAdmin->isMasterAdmin())) {
            $this->addFlash('error', 'You do not have permission to edit partners.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $form = $this->createForm(PartnerType::class, $partner, ['is_edit' => true]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Handle logo file upload
            $logoFile = $form->get('logoFile')->getData();
            if ($logoFile) {
                // Delete old logo if exists
                if ($partner->getLogoPath()) {
                    $oldLogoPath = $this->getParameter('kernel.project_dir') . '/public/images/partners/' . $partner->getLogoPath();
                    if (file_exists($oldLogoPath)) {
                        unlink($oldLogoPath);
                    }
                }

                $originalFilename = pathinfo($logoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
                $newFilename = $safeFilename . '-' . uniqid() . '.' . $logoFile->guessExtension();

                try {
                    $logoFile->move(
                        $this->getParameter('kernel.project_dir') . '/public/images/partners',
                        $newFilename
                    );
                    $partner->setLogoPath($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'Error uploading logo file: ' . $e->getMessage());
                    return $this->render('admin/partners/edit.html.twig', [
                        'form' => $form->createView(),
                        'partner' => $partner
                    ]);
                }
            }

            $this->entityManager->flush();

            $this->addFlash('success', 'Partner updated successfully!');
            return $this->redirectToRoute('admin_partners_show', ['id' => $partner->getId()]);
        }

        return $this->render('admin/partners/edit.html.twig', [
            'form' => $form->createView(),
            'partner' => $partner
        ]);
    }

    #[Route('/partners/{id}/delete', name: 'admin_partners_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function deletePartner(Partner $partner): Response
    {
        $partnerName = $partner->getName();

        // Delete logo file if it exists
        $logoPath = $this->getParameter('kernel.project_dir') . '/public/images/partners/' . $partner->getLogoPath();
        if (file_exists($logoPath)) {
            unlink($logoPath);
        }

        $this->entityManager->remove($partner);
        $this->entityManager->flush();

        return $this->json(['success' => true, 'message' => "Partner '{$partnerName}' deleted successfully!"]);
    }

    #[Route('/partners/{id}/toggle', name: 'admin_partners_toggle', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function togglePartner(Partner $partner): Response
    {
        $partner->setIsActive(!$partner->isActive());
        $this->entityManager->flush();

        $status = $partner->isActive() ? 'activated' : 'deactivated';
        return $this->json(['success' => true, 'message' => "Partner '{$partner->getName()}' {$status} successfully!"]);
    }

    #[Route('/partners/reorder', name: 'admin_partners_reorder', methods: ['POST'])]
    public function reorderPartners(Request $request): Response
    {
        $partnerIds = $request->request->all('partner_ids');

        if (empty($partnerIds)) {
            return $this->json(['success' => false, 'message' => 'No partner IDs provided']);
        }

        try {
            $this->partnerRepository->updateDisplayOrders($partnerIds);
            return $this->json(['success' => true, 'message' => 'Partners reordered successfully!']);
        } catch (\Exception $e) {
            return $this->json(['success' => false, 'message' => 'Error reordering partners: ' . $e->getMessage()]);
        }
    }

    // ==================== ENROLLMENT MANAGEMENT ====================

    #[Route('/enrollments', name: 'admin_enrollments_list')]
    public function enrollmentsList(Request $request, EnrollmentRepository $enrollmentRepository, PaymentRepository $paymentRepository): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.read')) {
            throw $this->createAccessDeniedException('You do not have permission to view enrollments.');
        }

        // Get filter parameters
        $courseFilter = $request->query->get('course');
        $userFilter = $request->query->get('user');
        $statusFilter = $request->query->get('status', 'all');
        $page = max(1, $request->query->getInt('page', 1));
        $limit = 20;

        // Build query
        $queryBuilder = $enrollmentRepository->createQueryBuilder('e')
            ->leftJoin('e.user', 'u')
            ->leftJoin('e.course', 'c')
            ->leftJoin('e.payment', 'p')
            ->addSelect('u', 'c', 'p')
            ->orderBy('e.enrolledAt', 'DESC');

        // Apply filters
        if ($courseFilter) {
            $queryBuilder->andWhere('c.code LIKE :course OR c.title LIKE :course')
                ->setParameter('course', '%' . $courseFilter . '%');
        }

        if ($userFilter) {
            $queryBuilder->andWhere('u.email LIKE :user OR u.firstName LIKE :user OR u.lastName LIKE :user')
                ->setParameter('user', '%' . $userFilter . '%');
        }

        if ($statusFilter !== 'all') {
            switch ($statusFilter) {
                case 'active':
                    $queryBuilder->andWhere('e.isActive = true');
                    break;
                case 'completed':
                    $queryBuilder->andWhere('e.completedAt IS NOT NULL');
                    break;
                case 'inactive':
                    $queryBuilder->andWhere('e.isActive = false');
                    break;
            }
        }

        // Get total count for pagination
        $totalQuery = clone $queryBuilder;
        $total = $totalQuery->select('COUNT(e.id)')->getQuery()->getSingleScalarResult();

        // Apply pagination
        $enrollments = $queryBuilder
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();

        // Calculate pagination info
        $totalPages = ceil($total / $limit);

        return $this->render('admin/enrollments/list.html.twig', [
            'enrollments' => $enrollments,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'total' => $total,
            'filters' => [
                'course' => $courseFilter,
                'user' => $userFilter,
                'status' => $statusFilter,
            ],
        ]);
    }

    #[Route('/enrollments/{id}', name: 'admin_enrollment_details', requirements: ['id' => '\d+'])]
    public function enrollmentDetails(int $id, EnrollmentRepository $enrollmentRepository): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.read')) {
            throw $this->createAccessDeniedException('You do not have permission to view enrollment details.');
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            throw $this->createNotFoundException('Enrollment not found.');
        }

        return $this->render('admin/enrollments/details.html.twig', [
            'enrollment' => $enrollment,
        ]);
    }

    #[Route('/enrollments/{courseCode}-{studentName}', name: 'admin_enrollment_details_by_code')]
    public function enrollmentDetailsByCode(string $courseCode, string $studentName, EnrollmentRepository $enrollmentRepository): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.read')) {
            throw $this->createAccessDeniedException('You do not have permission to view enrollment details.');
        }

        // Convert slugified student name back to searchable format
        $searchName = str_replace('-', ' ', $studentName);

        // Find enrollment by course code and student name
        $enrollment = $enrollmentRepository->createQueryBuilder('e')
            ->leftJoin('e.course', 'c')
            ->leftJoin('e.user', 'u')
            ->where('c.code = :courseCode')
            ->andWhere('CONCAT(LOWER(u.firstName), \' \', LOWER(u.lastName)) LIKE :studentName')
            ->setParameter('courseCode', $courseCode)
            ->setParameter('studentName', '%' . strtolower($searchName) . '%')
            ->getQuery()
            ->getOneOrNullResult();

        if (!$enrollment) {
            throw $this->createNotFoundException('Enrollment not found.');
        }

        return $this->render('admin/enrollments/details.html.twig', [
            'enrollment' => $enrollment,
        ]);
    }





    #[Route('/enrollments/create', name: 'admin_enrollment_create')]
    public function createEnrollment(Request $request, UserRepository $userRepository, CourseRepository $courseRepository, EnrollmentRepository $enrollmentRepository): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.create')) {
            throw $this->createAccessDeniedException('You do not have permission to create enrollments.');
        }

        if ($request->isMethod('POST')) {
            $userId = $request->request->get('user_id');
            $courseId = $request->request->get('course_id');

            $user = $userRepository->find($userId);
            $course = $courseRepository->find($courseId);

            if (!$user || !$course) {
                $this->addFlash('error', 'Invalid user or course selected.');
                return $this->redirectToRoute('admin_enrollment_create');
            }

            // Check if enrollment already exists
            $existingEnrollment = $enrollmentRepository->findEnrollmentByUserAndCourse($user, $course);
            if ($existingEnrollment) {
                $this->addFlash('error', 'User is already enrolled in this course.');
                return $this->redirectToRoute('admin_enrollment_create');
            }

            // Create enrollment
            $enrollment = new Enrollment();
            $enrollment->setUser($user);
            $enrollment->setCourse($course);

            $this->entityManager->persist($enrollment);
            $this->entityManager->flush();

            $this->addFlash('success', 'Enrollment created successfully.');
            return $this->redirectToRoute('admin_enrollments_list');
        }

        $users = $userRepository->findBy([], ['email' => 'ASC']);
        $courses = $courseRepository->findBy(['is_active' => true], ['title' => 'ASC']);

        return $this->render('admin/enrollments/create.html.twig', [
            'users' => $users,
            'courses' => $courses,
        ]);
    }

    #[Route('/enrollments/{id}/update-progress', name: 'admin_enrollment_update_progress', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function updateEnrollmentProgress(int $id, Request $request, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to update enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        $data = json_decode($request->getContent(), true);
        $progress = $data['progress'] ?? null;

        if ($progress === null || !is_numeric($progress) || $progress < 0 || $progress > 100) {
            return new JsonResponse(['error' => 'Invalid progress value. Must be between 0 and 100.'], 400);
        }

        $enrollment->setProgressPercentage((int)$progress);

        // If progress is 100%, mark as completed
        if ($progress == 100 && !$enrollment->isCompleted()) {
            $enrollment->markAsCompleted();
        }

        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Progress updated successfully.',
            'progress' => $enrollment->getProgressPercentage(),
            'isCompleted' => $enrollment->isCompleted()
        ]);
    }

    #[Route('/enrollments/{id}/mark-completed', name: 'admin_enrollment_mark_completed', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function markEnrollmentCompleted(int $id, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to update enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        if ($enrollment->isCompleted()) {
            return new JsonResponse(['error' => 'Enrollment is already completed.'], 400);
        }

        $enrollment->markAsCompleted();
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Enrollment marked as completed successfully.',
            'completedAt' => $enrollment->getCompletedAt()->format('M j, Y g:i A')
        ]);
    }

    #[Route('/enrollments/{id}/update', name: 'admin_enrollment_update', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function updateEnrollment(int $id, Request $request, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to update enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        $status = $request->request->get('status');
        $progress = $request->request->get('progress');

        if ($status) {
            $enrollment->setStatus($status);
        }

        if ($progress !== null) {
            $enrollment->setProgressPercentage((int)$progress);
        }

        $this->entityManager->flush();

        return new JsonResponse(['success' => true, 'message' => 'Enrollment updated successfully.']);
    }

    #[Route('/enrollments/{id}/block', name: 'admin_enrollment_block', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function blockEnrollment(int $id, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to update enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        $enrollment->setStatus('blocked');
        $this->entityManager->flush();

        return new JsonResponse(['success' => true, 'message' => 'Enrollment blocked successfully.']);
    }

    #[Route('/enrollments/{id}/unblock', name: 'admin_enrollment_unblock', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function unblockEnrollment(int $id, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to update enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        $enrollment->setStatus('active');
        $this->entityManager->flush();

        return new JsonResponse(['success' => true, 'message' => 'Enrollment unblocked successfully.']);
    }

    #[Route('/enrollments/{id}/delete', name: 'admin_enrollment_delete', requirements: ['id' => '\d+'], methods: ['DELETE'])]
    public function deleteEnrollment(int $id, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.delete')) {
            return new JsonResponse(['error' => 'You do not have permission to delete enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        $this->entityManager->remove($enrollment);
        $this->entityManager->flush();

        return new JsonResponse(['success' => true, 'message' => 'Enrollment deleted successfully.']);
    }

    #[Route('/enrollments/{id}/certify', name: 'admin_enrollment_certify', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function certifyEnrollment(int $id, EnrollmentRepository $enrollmentRepository): JsonResponse
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('enrollments.update')) {
            return new JsonResponse(['error' => 'You do not have permission to certify enrollments.'], 403);
        }

        $enrollment = $enrollmentRepository->find($id);
        if (!$enrollment) {
            return new JsonResponse(['error' => 'Enrollment not found.'], 404);
        }

        // Check if already certified
        $certificationRepository = $this->entityManager->getRepository(\App\Entity\Certification::class);
        if ($certificationRepository->isEnrollmentCertified($enrollment->getId())) {
            return new JsonResponse(['error' => 'This enrollment is already certified.'], 400);
        }

        // Create certification
        $certification = new \App\Entity\Certification();
        $certification->setEnrollment($enrollment);
        $certification->setStudent($enrollment->getUser());
        $certification->setCourse($enrollment->getCourse());

        // Mark enrollment as certified and set progress to 100%
        $enrollment->setStatus('certified');
        $enrollment->setProgressPercentage(100);

        // Set completion date if not already set
        if ($enrollment->getCompletedAt() === null) {
            $enrollment->setCompletedAt(new \DateTimeImmutable());
        }

        $this->entityManager->persist($certification);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Enrollment certified successfully!'
        ]);
    }

    // ==================== CERTIFICATION MANAGEMENT ====================

    #[Route('/certifications', name: 'admin_certifications')]
    public function certifications(Request $request): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('certifications.view')) {
            $this->addFlash('error', 'You do not have permission to view certifications.');
            return $this->redirectToRoute('admin_dashboard');
        }

        $page = $request->query->getInt('page', 1);
        $limit = 20;

        $certificationRepository = $this->entityManager->getRepository(\App\Entity\Certification::class);
        $certifications = $certificationRepository->findWithPagination($page, $limit);
        $totalCertifications = $certificationRepository->countTotal();
        $totalPages = ceil($totalCertifications / $limit);

        return $this->render('admin/certifications/list.html.twig', [
            'certifications' => $certifications,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_certifications' => $totalCertifications,
        ]);
    }

    #[Route('/certifications/{id}', name: 'admin_certification_details', requirements: ['id' => '\d+'])]
    public function certificationDetails(int $id): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('certifications.view')) {
            $this->addFlash('error', 'You do not have permission to view certifications.');
            return $this->redirectToRoute('admin_certifications');
        }

        $certificationRepository = $this->entityManager->getRepository(\App\Entity\Certification::class);
        $certification = $certificationRepository->find($id);

        if (!$certification) {
            throw $this->createNotFoundException('Certification not found.');
        }

        return $this->render('admin/certifications/details.html.twig', [
            'certification' => $certification,
        ]);
    }

    #[Route('/certifications/{studentName}-{courseCode}', name: 'admin_certification_details_by_readable', requirements: ['studentName' => '[a-z0-9\-]+', 'courseCode' => '[A-Z0-9]+'])]
    public function certificationDetailsByReadable(string $studentName, string $courseCode): Response
    {
        // Check permissions
        $user = $this->getUser();
        if (!$user instanceof Admin || !$user->hasPermission('certifications.view')) {
            $this->addFlash('error', 'You do not have permission to view certifications.');
            return $this->redirectToRoute('admin_certifications');
        }

        // Find certification by student name and course code
        $certificationRepository = $this->entityManager->getRepository(\App\Entity\Certification::class);
        $certification = $certificationRepository->findByStudentNameAndCourseCode($studentName, $courseCode);

        if (!$certification) {
            throw $this->createNotFoundException('Certification not found.');
        }

        return $this->render('admin/certifications/details.html.twig', [
            'certification' => $certification,
        ]);
    }

    private function generateUniqueCode(string $prefix, string $entityClass): string
    {
        $counter = 1;
        do {
            $code = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $existing = $this->entityManager->getRepository($entityClass)->findOneBy(['code' => $code]);
            $counter++;
        } while ($existing && $counter < 1000);

        if ($counter >= 1000) {
            throw new \RuntimeException('Unable to generate unique code for ' . $entityClass);
        }

        return $code;
    }
}
