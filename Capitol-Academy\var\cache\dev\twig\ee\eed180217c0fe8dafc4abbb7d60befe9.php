<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/edit.html.twig */
class __TwigTemplate_239a116a595dffe1bf0559f405a89c5c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Instructor - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Instructor";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Edit Instructor</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Instructor: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 37, $this->source); })()), "name", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("instructor_edit"), "html", null, true);
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Name and Email Row -->
                            <div class=\"row\">
                                <!-- Full Name -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"name\" class=\"form-label\">
                                            <i class=\"fas fa-user text-primary mr-1\"></i>
                                            Full Name <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"name\"
                                               name=\"name\"
                                               value=\"";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 75, $this->source); })()), "name", [], "any", false, false, false, 75), "html", null, true);
        yield "\"
                                               placeholder=\"Enter instructor full name\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's full name.
                                        </div>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"email\" class=\"form-label\">
                                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                            Email Address <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"email\"
                                               class=\"form-control enhanced-field\"
                                               id=\"email\"
                                               name=\"email\"
                                               value=\"";
        // line 97
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 97, $this->source); })()), "email", [], "any", false, false, false, 97), "html", null, true);
        yield "\"
                                               placeholder=\"<EMAIL>\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid email address.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Specialization and Phone Row -->
                            <div class=\"row\">
                                <!-- Specialization -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"specialization\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Specialization <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"specialization\"
                                               name=\"specialization\"
                                               value=\"";
        // line 122
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 122, $this->source); })()), "specialization", [], "any", false, false, false, 122), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., Forex Trading, Technical Analysis\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's specialization.
                                        </div>
                                    </div>
                                </div>

                                <!-- Phone -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"phone\" class=\"form-label\">
                                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                                            Phone Number
                                        </label>
                                        <input type=\"tel\"
                                               class=\"form-control enhanced-field\"
                                               id=\"phone\"
                                               name=\"phone\"
                                               value=\"";
        // line 144
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 144, $this->source); })()), "phone", [], "any", false, false, false, 144), "html", null, true);
        yield "\"
                                               placeholder=\"+****************\"
                                               maxlength=\"20\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>
                            </div>

                            <!-- LinkedIn URL and Display Order Row -->
                            <div class=\"row\">
                                <!-- LinkedIn URL -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"linkedinUrl\" class=\"form-label\">
                                            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
                                            LinkedIn Profile URL
                                        </label>
                                        <input type=\"url\"
                                               class=\"form-control enhanced-field\"
                                               id=\"linkedinUrl\"
                                               name=\"linkedinUrl\"
                                               value=\"";
        // line 165
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 165, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 165), "html", null, true);
        yield "\"
                                               placeholder=\"https://linkedin.com/in/username\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"displayOrder\"
                                               name=\"displayOrder\"
                                               value=\"";
        // line 183
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 183, $this->source); })()), "displayOrder", [], "any", false, false, false, 183), "html", null, true);
        yield "\"
                                               placeholder=\"1\"
                                               min=\"1\"
                                               max=\"999\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"form-group\">
                                <label for=\"bio\" class=\"form-label\">
                                    <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                    Biography <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"bio\"
                                          name=\"bio\"
                                          rows=\"4\"
                                          placeholder=\"Enter instructor biography and background...\"
                                          required
                                          maxlength=\"2000\"
                                          style=\"border: 2px solid #ced4da; border-radius: 8px; font-size: 1rem;\">";
        // line 205
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 205, $this->source); })()), "bio", [], "any", false, false, false, 205), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide the instructor's biography.
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Qualifications <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"qualifications-container\">
                                    ";
        // line 218
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 218, $this->source); })()), "qualifications", [], "any", false, false, false, 218)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 219
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 219, $this->source); })()), "qualifications", [], "any", false, false, false, 219));
            foreach ($context['_seq'] as $context["_key"] => $context["qualification"]) {
                // line 220
                yield "                                            <div class=\"input-group mb-2 qualification-item\">
                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" value=\"";
                // line 221
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["qualification"], "html", null, true);
                yield "\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                                <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['qualification'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 227
            yield "                                    ";
        } else {
            // line 228
            yield "                                        <div class=\"input-group mb-2 qualification-item\">
                                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                            <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>
                                    ";
        }
        // line 235
        yield "                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-qualification\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Qualification
                                </button>
                            </div>

                            <!-- Achievements -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-trophy text-primary mr-1\"></i>
                                    Achievements <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"achievements-container\">
                                    ";
        // line 248
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 248, $this->source); })()), "achievements", [], "any", false, false, false, 248)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 249
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 249, $this->source); })()), "achievements", [], "any", false, false, false, 249));
            foreach ($context['_seq'] as $context["_key"] => $context["achievement"]) {
                // line 250
                yield "                                            <div class=\"input-group mb-2 achievement-item\">
                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" value=\"";
                // line 251
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["achievement"], "html", null, true);
                yield "\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                                <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['achievement'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 257
            yield "                                    ";
        } else {
            // line 258
            yield "                                        <div class=\"input-group mb-2 achievement-item\">
                                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                            <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>
                                    ";
        }
        // line 265
        yield "                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-achievement\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Achievement
                                </button>
                            </div>

                            <!-- Profile Image -->
                            <div class=\"form-group\">
                                <label for=\"profileImageFile\" class=\"form-label\">
                                    <i class=\"fas fa-camera text-primary mr-1\"></i>
                                    Profile Image
                                </label>

                                ";
        // line 278
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 278, $this->source); })()), "profileImage", [], "any", false, false, false, 278)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 279
            yield "                                    <div class=\"mb-3\">
                                        <img src=\"";
            // line 280
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 280, $this->source); })()), "profileImage", [], "any", false, false, false, 280))), "html", null, true);
            yield "\"
                                             alt=\"";
            // line 281
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 281, $this->source); })()), "name", [], "any", false, false, false, 281), "html", null, true);
            yield "\"
                                             class=\"img-fluid rounded-circle\"
                                             style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                ";
        }
        // line 286
        yield "
                                <input type=\"file\"
                                       class=\"form-control enhanced-field\"
                                       id=\"profileImageFile\"
                                       name=\"profileImageFile\"
                                       accept=\"image/*\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">
                                    Upload a new image to replace the current one. Recommended size: 300x300px. Supported formats: JPG, PNG, GIF.
                                </small>

                                <!-- Image Preview -->
                                <div id=\"profile-preview\" class=\"mt-3\" style=\"display: none;\">
                                    <img src=\"\" alt=\"Preview\" class=\"img-fluid rounded-circle\" style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                </div>
                            </div>
                        </div>
                    </div>
            </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Instructor
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 315
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>

        </form>
    </div>
</div>

<style>
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-field:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.form-label {
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-right: 0.5rem;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
}

.qualification-item, .achievement-item {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced field styling
    \$('.enhanced-field').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Add qualification
    \$('.add-qualification').on('click', function() {
        const container = \$('#qualifications-container');
        const newItem = `
            <div class=\"input-group mb-2 qualification-item\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                    <i class=\"fas fa-times\"></i>
                </button>
            </div>
        `;
        container.append(newItem);
    });

    // Remove qualification
    \$(document).on('click', '.remove-qualification', function() {
        if (\$('.qualification-item').length > 1) {
            \$(this).closest('.qualification-item').remove();
        } else {
            alert('At least one qualification is required.');
        }
    });

    // Add achievement
    \$('.add-achievement').on('click', function() {
        const container = \$('#achievements-container');
        const newItem = `
            <div class=\"input-group mb-2 achievement-item\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                    <i class=\"fas fa-times\"></i>
                </button>
            </div>
        `;
        container.append(newItem);
    });

    // Remove achievement
    \$(document).on('click', '.remove-achievement', function() {
        if (\$('.achievement-item').length > 1) {
            \$(this).closest('.achievement-item').remove();
        } else {
            alert('At least one achievement is required.');
        }
    });

    // Profile image preview
    \$('#profileImageFile').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#profile-preview img').attr('src', e.target.result);
                \$('#profile-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#profile-preview').hide();
        }
    });

    // Enhanced form validation with loading states
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Instructor';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Instructor...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  565 => 315,  534 => 286,  526 => 281,  522 => 280,  519 => 279,  517 => 278,  502 => 265,  493 => 258,  490 => 257,  478 => 251,  475 => 250,  470 => 249,  468 => 248,  453 => 235,  444 => 228,  441 => 227,  429 => 221,  426 => 220,  421 => 219,  419 => 218,  403 => 205,  378 => 183,  357 => 165,  333 => 144,  308 => 122,  280 => 97,  255 => 75,  234 => 57,  217 => 43,  208 => 37,  198 => 29,  188 => 25,  185 => 24,  181 => 23,  178 => 22,  168 => 18,  165 => 17,  161 => 16,  157 => 14,  144 => 13,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Instructor - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Instructor{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_instructor_index') }}\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Edit Instructor</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Instructor: {{ instructor.name }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"{{ path('admin_instructor_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('instructor_edit') }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Name and Email Row -->
                            <div class=\"row\">
                                <!-- Full Name -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"name\" class=\"form-label\">
                                            <i class=\"fas fa-user text-primary mr-1\"></i>
                                            Full Name <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"name\"
                                               name=\"name\"
                                               value=\"{{ instructor.name }}\"
                                               placeholder=\"Enter instructor full name\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's full name.
                                        </div>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"email\" class=\"form-label\">
                                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                            Email Address <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"email\"
                                               class=\"form-control enhanced-field\"
                                               id=\"email\"
                                               name=\"email\"
                                               value=\"{{ instructor.email }}\"
                                               placeholder=\"<EMAIL>\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid email address.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Specialization and Phone Row -->
                            <div class=\"row\">
                                <!-- Specialization -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"specialization\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Specialization <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"specialization\"
                                               name=\"specialization\"
                                               value=\"{{ instructor.specialization }}\"
                                               placeholder=\"e.g., Forex Trading, Technical Analysis\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's specialization.
                                        </div>
                                    </div>
                                </div>

                                <!-- Phone -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"phone\" class=\"form-label\">
                                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                                            Phone Number
                                        </label>
                                        <input type=\"tel\"
                                               class=\"form-control enhanced-field\"
                                               id=\"phone\"
                                               name=\"phone\"
                                               value=\"{{ instructor.phone }}\"
                                               placeholder=\"+****************\"
                                               maxlength=\"20\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>
                            </div>

                            <!-- LinkedIn URL and Display Order Row -->
                            <div class=\"row\">
                                <!-- LinkedIn URL -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"linkedinUrl\" class=\"form-label\">
                                            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
                                            LinkedIn Profile URL
                                        </label>
                                        <input type=\"url\"
                                               class=\"form-control enhanced-field\"
                                               id=\"linkedinUrl\"
                                               name=\"linkedinUrl\"
                                               value=\"{{ instructor.linkedinUrl }}\"
                                               placeholder=\"https://linkedin.com/in/username\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"displayOrder\"
                                               name=\"displayOrder\"
                                               value=\"{{ instructor.displayOrder }}\"
                                               placeholder=\"1\"
                                               min=\"1\"
                                               max=\"999\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    </div>
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"form-group\">
                                <label for=\"bio\" class=\"form-label\">
                                    <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                    Biography <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"bio\"
                                          name=\"bio\"
                                          rows=\"4\"
                                          placeholder=\"Enter instructor biography and background...\"
                                          required
                                          maxlength=\"2000\"
                                          style=\"border: 2px solid #ced4da; border-radius: 8px; font-size: 1rem;\">{{ instructor.bio }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide the instructor's biography.
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Qualifications <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"qualifications-container\">
                                    {% if instructor.qualifications %}
                                        {% for qualification in instructor.qualifications %}
                                            <div class=\"input-group mb-2 qualification-item\">
                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" value=\"{{ qualification }}\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                                <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"input-group mb-2 qualification-item\">
                                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                            <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-qualification\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Qualification
                                </button>
                            </div>

                            <!-- Achievements -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-trophy text-primary mr-1\"></i>
                                    Achievements <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"achievements-container\">
                                    {% if instructor.achievements %}
                                        {% for achievement in instructor.achievements %}
                                            <div class=\"input-group mb-2 achievement-item\">
                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" value=\"{{ achievement }}\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                                <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"input-group mb-2 achievement-item\">
                                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                                            <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-achievement\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Achievement
                                </button>
                            </div>

                            <!-- Profile Image -->
                            <div class=\"form-group\">
                                <label for=\"profileImageFile\" class=\"form-label\">
                                    <i class=\"fas fa-camera text-primary mr-1\"></i>
                                    Profile Image
                                </label>

                                {% if instructor.profileImage %}
                                    <div class=\"mb-3\">
                                        <img src=\"{{ asset('uploads/instructors/' ~ instructor.profileImage) }}\"
                                             alt=\"{{ instructor.name }}\"
                                             class=\"img-fluid rounded-circle\"
                                             style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                {% endif %}

                                <input type=\"file\"
                                       class=\"form-control enhanced-field\"
                                       id=\"profileImageFile\"
                                       name=\"profileImageFile\"
                                       accept=\"image/*\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">
                                    Upload a new image to replace the current one. Recommended size: 300x300px. Supported formats: JPG, PNG, GIF.
                                </small>

                                <!-- Image Preview -->
                                <div id=\"profile-preview\" class=\"mt-3\" style=\"display: none;\">
                                    <img src=\"\" alt=\"Preview\" class=\"img-fluid rounded-circle\" style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                </div>
                            </div>
                        </div>
                    </div>
            </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Instructor
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_instructor_index') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>

        </form>
    </div>
</div>

<style>
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-field:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.form-label {
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-right: 0.5rem;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
}

.qualification-item, .achievement-item {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced field styling
    \$('.enhanced-field').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Add qualification
    \$('.add-qualification').on('click', function() {
        const container = \$('#qualifications-container');
        const newItem = `
            <div class=\"input-group mb-2 qualification-item\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"qualifications[]\" placeholder=\"e.g., CFA Level III, MBA Finance\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                <button class=\"btn btn-outline-danger remove-qualification\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                    <i class=\"fas fa-times\"></i>
                </button>
            </div>
        `;
        container.append(newItem);
    });

    // Remove qualification
    \$(document).on('click', '.remove-qualification', function() {
        if (\$('.qualification-item').length > 1) {
            \$(this).closest('.qualification-item').remove();
        } else {
            alert('At least one qualification is required.');
        }
    });

    // Add achievement
    \$('.add-achievement').on('click', function() {
        const container = \$('#achievements-container');
        const newItem = `
            <div class=\"input-group mb-2 achievement-item\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"achievements[]\" placeholder=\"e.g., Top 1% Trading Performance 2023\" required style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;\">
                <button class=\"btn btn-outline-danger remove-achievement\" type=\"button\" style=\"border-radius: 0 0.375rem 0.375rem 0;\">
                    <i class=\"fas fa-times\"></i>
                </button>
            </div>
        `;
        container.append(newItem);
    });

    // Remove achievement
    \$(document).on('click', '.remove-achievement', function() {
        if (\$('.achievement-item').length > 1) {
            \$(this).closest('.achievement-item').remove();
        } else {
            alert('At least one achievement is required.');
        }
    });

    // Profile image preview
    \$('#profileImageFile').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#profile-preview img').attr('src', e.target.result);
                \$('#profile-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#profile-preview').hide();
        }
    });

    // Enhanced form validation with loading states
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Instructor';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Instructor...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
{% endblock %}
", "admin/instructor/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\edit.html.twig");
    }
}
