<?php

namespace Container8BoVpN5;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getVideoRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\VideoRepository' shared autowired service.
     *
     * @return \App\Repository\VideoRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'VideoRepository.php';

        return $container->privates['App\\Repository\\VideoRepository'] = new \App\Repository\VideoRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
