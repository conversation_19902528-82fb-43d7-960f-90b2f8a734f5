{% extends 'admin/base.html.twig' %}

{% block title %}Categories Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Categories Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Categories</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Categories Management',
    'page_icon': 'fas fa-folder',
    'search_placeholder': 'Search categories by name or description...',
    'create_button': {
        'url': path('admin_category_new'),
        'text': 'Add New Category',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Categories',
            'value': categories|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': categories|filter(category => category.active)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'For Courses',
            'value': categories|filter(category => category.displayInCourses)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#007bff',
            'gradient': 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'
        },
        {
            'title': 'For Videos',
            'value': categories|filter(category => category.displayInVideos)|length,
            'icon': 'fas fa-video',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Display Options'},
            {'text': 'Actions', 'style': 'width: 250px;'}
        ] %}

        {% set table_rows = [] %}
        {% for category in categories %}
            {% set row_cells = [
                {
                    'content': '<h6 class="category-name mb-0 font-weight-bold text-dark">' ~ category.name ~ '</h6>'
                },
                {
                    'content': '<div class="d-flex gap-1">
                        ' ~ (category.displayInCourses ? '<span class="badge" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;"><i class="fas fa-graduation-cap mr-1"></i>Courses</span>' : '') ~ '
                        ' ~ (category.displayInVideos ? '<span class="badge" style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;"><i class="fas fa-video mr-1"></i>Videos</span>' : '') ~ '
                        ' ~ (not category.displayInCourses and not category.displayInVideos ? '<span class="badge" style="background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">None</span>' : '') ~ '
                    </div>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_category_show', {'id': category.id}) ~ '" class="btn btn-sm shadow-sm" style="background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Category"><i class="fas fa-eye"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: ' ~ (category.displayInCourses ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInCourses ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (category.displayInCourses ? 'Deactivate' : 'Activate') ~ ' in Courses" onclick="toggleCategoryInCourses(' ~ category.id ~ ', \'' ~ category.name ~ '\', ' ~ (category.displayInCourses ? 'true' : 'false') ~ ')"><i class="fas fa-graduation-cap"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: ' ~ (category.displayInVideos ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInVideos ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (category.displayInVideos ? 'Deactivate' : 'Activate') ~ ' in Videos" onclick="toggleCategoryInVideos(' ~ category.id ~ ', \'' ~ category.name ~ '\', ' ~ (category.displayInVideos ? 'true' : 'false') ~ ')"><i class="fas fa-video"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: #dc3545; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Category" onclick="deleteCategoryConfirm(' ~ category.id ~ ', \'' ~ category.name ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'category-row',
            'empty_message': 'No categories found',
            'empty_icon': 'fas fa-folder',
            'empty_description': 'Get started by adding your first category.',
            'search_config': {
                'fields': ['.category-name', '.category-description']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.category-row',
        ['.category-name', '.category-description']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});





// Category management functions - Direct toggle without confirmation
function toggleCategoryInCourses(categoryId, categoryName, currentStatus) {
    executeCategoryCoursesToggle(categoryId);
}

function toggleCategoryInVideos(categoryId, categoryName, currentStatus) {
    executeCategoryVideosToggle(categoryId);
}

// Actual execution functions
function executeCategoryCoursesToggle(categoryId) {
    fetch(`/admin/categories/${categoryId}/toggle-courses`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the category courses status.');
    });
}

function executeCategoryVideosToggle(categoryId) {
    fetch(`/admin/categories/${categoryId}/toggle-videos`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the category videos status.');
    });
}

function deleteCategoryConfirm(categoryId, categoryName) {
    AdminPageUtils.showDeleteModal(categoryId, categoryName, deleteCategory);
}

function deleteCategory(categoryId) {
    fetch(`/admin/categories/${categoryId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the category');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the category.');
    });
}
</script>
{% endblock %}
