{% extends 'admin/base.html.twig' %}

{% block title %}Promotional Banners - Capitol Academy Admin{% endblock %}

{% block page_title %}Promotional Banners Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Promotional Banners</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Promotional Banners Management',
    'page_icon': 'fas fa-bullhorn',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_promotional_banner_create'),
        'text': 'Create New Banner',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Banners',
            'value': banners|length,
            'icon': 'fas fa-bullhorn',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': banners|filter(banner => banner.isActive)|length,
            'icon': 'fas fa-eye',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': banners|filter(banner => not banner.isActive)|length,
            'icon': 'fas fa-eye-slash',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': banners|filter(banner => banner.createdAt and banner.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Title'},
            {'text': 'End Date'},
            {'text': 'Status'},
            {'text': 'Created'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for banner in banners %}
            {% set row_cells = [
                {
                    'content': '<div class="d-flex align-items-center">
                        <div class="rounded me-3" style="width: 4px; height: 40px; background-color: ' ~ banner.backgroundColor ~ '"></div>
                        <div>
                            <h6 class="banner-title mb-0 font-weight-bold text-dark">' ~ banner.title ~ '</h6>
                            ' ~ (banner.description ? '<small class="text-muted">' ~ (banner.description|length > 60 ? banner.description|slice(0, 60) ~ '...' : banner.description) ~ '</small>' : '') ~ '
                        </div>
                    </div>'
                },
                {
                    'content': banner.endDate ?
                        '<span class="text-dark font-weight-medium">' ~ banner.endDate|date('M d, Y H:i') ~ '</span>' :
                        '<span class="text-muted">No expiration</span>'
                },
                {
                    'content': banner.isCurrentlyValid ?
                        '<span class="badge" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>' :
                        (banner.isActive ?
                            '<span class="badge" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-clock mr-1"></i> Expired</span>' :
                            '<span class="badge" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-ban mr-1"></i> Disabled</span>'
                        )
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ banner.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_promotional_banner_edit', {'id': banner.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Banner"><i class="fas fa-edit"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, ' ~ (banner.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (banner.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (banner.isActive ? 'Deactivate' : 'Activate') ~ ' Banner" onclick="toggleBannerStatus(' ~ banner.id ~ ', \'' ~ banner.title ~ '\', ' ~ banner.isActive ~ ')"><i class="fas fa-' ~ (banner.isActive ? 'eye-slash' : 'eye') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Banner" onclick="deleteBanner(' ~ banner.id ~ ', \'' ~ banner.title ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'banner-row',
            'empty_message': 'No promotional banners found',
            'empty_icon': 'fas fa-bullhorn',
            'empty_description': 'Get started by creating your first promotional banner.',
            'search_config': {
                'fields': ['.banner-title', '.banner-message']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.banner-row',
        ['.banner-title', '.banner-message']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Banner management functions
function toggleBannerStatus(bannerId, bannerTitle, isActive) {
    showStatusModal(bannerTitle, isActive, function() {
        executeBannerStatusToggle(bannerId);
    });
}

function deleteBanner(bannerId, bannerTitle) {
    showDeleteModal(bannerTitle, function() {
        executeBannerDelete(bannerId);
    });
}

// Actual execution functions
function executeBannerStatusToggle(bannerId) {
    fetch(`/admin/promotional-banners/${bannerId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the banner status');
    });
}

function executeBannerDelete(bannerId) {
    fetch(`/admin/promotional-banners/${bannerId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `_token=${encodeURIComponent('{{ csrf_token('promotional_banner_delete') }}')}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the banner');
    });
}
</script>
{% endblock %}
